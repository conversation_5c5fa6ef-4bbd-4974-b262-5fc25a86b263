//
//  MainViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class MainViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    private let newRaceButton = UIButton(type: .system)
    private let strategySimulatorButton = UIButton(type: .system)
    private let currentRaceCard = UIView()
    private let currentRaceLabel = UILabel()
    private let currentRaceStatusLabel = UILabel()
    private let continueRaceButton = UIButton(type: .system)
    
    private let raceHistoryLabel = UILabel()
    private let raceHistoryTableView = UITableView()
    private let emptyStateLabel = UILabel()
    
    // MARK: - Properties
    
    private let raceManager = RaceManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupRaceManager()
        updateUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateUI()
        raceHistoryTableView.reloadData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        navigationController?.setNavigationBarHidden(true, animated: false)
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Dragon Boat Timer"
        titleLabel.font = UIFont.systemFont(ofSize: 32, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Professional Race Recording System"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        contentView.addSubview(subtitleLabel)
        
        // Setup new race button
        newRaceButton.setTitle("Create New Race", for: .normal)
        newRaceButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        newRaceButton.setTitleColor(.white, for: .normal)
        newRaceButton.backgroundColor = UIColor.dragonAccent
        newRaceButton.layer.cornerRadius = 12
        newRaceButton.addTarget(self, action: #selector(createNewRacePressed), for: .touchUpInside)
        contentView.addSubview(newRaceButton)

        // Setup strategy simulator button
        strategySimulatorButton.setTitle("Strategy Simulator", for: .normal)
        strategySimulatorButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        strategySimulatorButton.setTitleColor(.white, for: .normal)
        strategySimulatorButton.backgroundColor = UIColor.dragonAccentSecondary
        strategySimulatorButton.layer.cornerRadius = 12
        strategySimulatorButton.addTarget(self, action: #selector(strategySimulatorPressed), for: .touchUpInside)
        contentView.addSubview(strategySimulatorButton)
        
        // Setup current race card
        currentRaceCard.backgroundColor = UIColor.dragonCardBackground
        currentRaceCard.layer.cornerRadius = 12
        currentRaceCard.layer.borderWidth = 1
        currentRaceCard.layer.borderColor = UIColor.dragonBorder.cgColor
        currentRaceCard.isHidden = true
        contentView.addSubview(currentRaceCard)
        
        currentRaceLabel.text = "Current Race"
        currentRaceLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        currentRaceLabel.textColor = UIColor.dragonTextPrimary
        currentRaceCard.addSubview(currentRaceLabel)
        
        currentRaceStatusLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        currentRaceStatusLabel.textColor = UIColor.dragonTextSecondary
        currentRaceCard.addSubview(currentRaceStatusLabel)
        
        continueRaceButton.setTitle("Continue", for: .normal)
        continueRaceButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        continueRaceButton.setTitleColor(.white, for: .normal)
        continueRaceButton.backgroundColor = UIColor.dragonAccentSecondary
        continueRaceButton.layer.cornerRadius = 8
        continueRaceButton.addTarget(self, action: #selector(continueRacePressed), for: .touchUpInside)
        currentRaceCard.addSubview(continueRaceButton)
        
        // Setup race history
        raceHistoryLabel.text = "Race History"
        raceHistoryLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        raceHistoryLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(raceHistoryLabel)
        
        raceHistoryTableView.backgroundColor = .clear
        raceHistoryTableView.separatorStyle = .none
        raceHistoryTableView.delegate = self
        raceHistoryTableView.dataSource = self
        raceHistoryTableView.register(RaceHistoryCell.self, forCellReuseIdentifier: "RaceHistoryCell")
        contentView.addSubview(raceHistoryTableView)
        
        emptyStateLabel.text = "No race history yet.\nCreate your first race to get started!"
        emptyStateLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        emptyStateLabel.textColor = UIColor.dragonTextTertiary
        emptyStateLabel.textAlignment = .center
        emptyStateLabel.numberOfLines = 0
        emptyStateLabel.isHidden = true
        contentView.addSubview(emptyStateLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        newRaceButton.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(40)
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalTo(view.snp.centerX).offset(-10)
            make.height.equalTo(56)
        }

        strategySimulatorButton.snp.makeConstraints { make in
            make.top.equalTo(newRaceButton)
            make.leading.equalTo(view.snp.centerX).offset(10)
            make.trailing.equalToSuperview().offset(-20)
            make.height.equalTo(56)
        }
        
        currentRaceCard.snp.makeConstraints { make in
            make.top.equalTo(strategySimulatorButton.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(100)
        }
        
        currentRaceLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        currentRaceStatusLabel.snp.makeConstraints { make in
            make.top.equalTo(currentRaceLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(continueRaceButton.snp.leading).offset(-16)
        }
        
        continueRaceButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(80)
            make.height.equalTo(36)
        }
        
        raceHistoryLabel.snp.makeConstraints { make in
            make.top.equalTo(currentRaceCard.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        raceHistoryTableView.snp.makeConstraints { make in
            make.top.equalTo(raceHistoryLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(300)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        emptyStateLabel.snp.makeConstraints { make in
            make.center.equalTo(raceHistoryTableView)
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
    
    private func setupRaceManager() {
        raceManager.delegate = self
    }
    
    // MARK: - Actions
    
    @objc private func createNewRacePressed() {
        let createRaceVC = CreateRaceViewController()
        let navController = UINavigationController(rootViewController: createRaceVC)
        present(navController, animated: true)
    }
    
    @objc private func continueRacePressed() {
        guard let currentRace = raceManager.currentRace else { return }

        if currentRace.status == .created {
            let createRaceVC = CreateRaceViewController(existingRace: currentRace)
            let navController = UINavigationController(rootViewController: createRaceVC)
            present(navController, animated: true)
        } else if currentRace.status == .inProgress {
            let timerVC = RaceTimerViewController(race: currentRace)
            navigationController?.pushViewController(timerVC, animated: true)
        }
    }

    @objc private func strategySimulatorPressed() {
        print("Strategy Simulator button pressed!")
        let strategyVC = StrategySimulatorViewController()
        navigationController?.pushViewController(strategyVC, animated: true)
    }
    
    // MARK: - UI Updates
    
    private func updateUI() {
        updateCurrentRaceCard()
        updateRaceHistory()
    }
    
    private func updateCurrentRaceCard() {
        if let currentRace = raceManager.currentRace, currentRace.status != .completed {
            currentRaceCard.isHidden = false
            currentRaceLabel.text = currentRace.name
            
            let statusText: String
            let buttonTitle: String
            
            switch currentRace.status {
            case .created:
                statusText = "Ready to start • \(currentRace.teams.count) teams"
                buttonTitle = "Setup"
            case .inProgress:
                let finishedCount = currentRace.finishedTeamsCount
                let totalCount = currentRace.teams.count
                statusText = "In progress • \(finishedCount)/\(totalCount) finished"
                buttonTitle = "Continue"
            case .completed:
                statusText = "Completed"
                buttonTitle = "View"
            }
            
            currentRaceStatusLabel.text = statusText
            continueRaceButton.setTitle(buttonTitle, for: .normal)
        } else {
            currentRaceCard.isHidden = true
        }
        
        // Update constraints
        let topConstraint = currentRaceCard.isHidden ? strategySimulatorButton.snp.bottom : currentRaceCard.snp.bottom
        raceHistoryLabel.snp.remakeConstraints { make in
            make.top.equalTo(topConstraint).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
        }
    }
    
    private func updateRaceHistory() {
        let hasHistory = !raceManager.completedRaces.isEmpty
        emptyStateLabel.isHidden = hasHistory
        raceHistoryTableView.isHidden = !hasHistory
    }
}

// MARK: - RaceManagerDelegate

extension MainViewController: RaceManagerDelegate {
    func raceManager(_ manager: RaceManager, didUpdateRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()
        }
    }

    func raceManager(_ manager: RaceManager, didFinishTeam team: Team, in race: RaceRecord) {
        // Handle team finish if needed
    }

    func raceManager(_ manager: RaceManager, didCompleteRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()
            self.raceHistoryTableView.reloadData()
        }
    }
}

// MARK: - UITableViewDataSource

extension MainViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return raceManager.completedRaces.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "RaceHistoryCell", for: indexPath) as! RaceHistoryCell
        let race = raceManager.completedRaces[indexPath.row]
        cell.configure(with: race)
        return cell
    }
}

// MARK: - UITableViewDelegate

extension MainViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let race = raceManager.completedRaces[indexPath.row]
        let resultsVC = RaceResultsViewController(race: race)
        navigationController?.pushViewController(resultsVC, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let race = raceManager.completedRaces[indexPath.row]

            let alert = UIAlertController(title: "Delete Race",
                                        message: "Are you sure you want to delete this race record?",
                                        preferredStyle: .alert)

            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                self.raceManager.deleteRace(race)
                self.updateUI()
                tableView.deleteRows(at: [indexPath], with: .fade)
            })

            present(alert, animated: true)
        }
    }
}

// MARK: - RaceHistoryCell

class RaceHistoryCell: UITableViewCell {

    private let containerView = UIView()
    private let nameLabel = UILabel()
    private let dateLabel = UILabel()
    private let teamsLabel = UILabel()
    private let winnerLabel = UILabel()
    private let chevronImageView = UIImageView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(containerView)

        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        nameLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(nameLabel)

        dateLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        dateLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(dateLabel)

        teamsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        teamsLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(teamsLabel)

        winnerLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        winnerLabel.textColor = UIColor.dragonAccentSecondary
        containerView.addSubview(winnerLabel)

        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        containerView.addSubview(chevronImageView)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 20, bottom: 4, right: 20))
        }

        nameLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(12)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }

        dateLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.leading.equalToSuperview().offset(12)
        }

        teamsLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.leading.equalToSuperview().offset(12)
        }

        winnerLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }

        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-12)
            make.width.height.equalTo(16)
        }
    }

    func configure(with race: RaceRecord) {
        nameLabel.text = race.name
        dateLabel.text = TimeFormatter.formatRaceDate(race.createdAt)
        teamsLabel.text = "\(race.teams.count) teams"

        if let winner = race.winner {
            winnerLabel.text = "🏆 \(winner.name)"
        } else {
            winnerLabel.text = "No winner"
        }
    }
}
