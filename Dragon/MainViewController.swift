//
//  MainViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class MainViewController: UIViewController {

    // MARK: - UI Components

    private let tableView = UITableView(frame: .zero, style: .grouped)

    // MARK: - Properties

    private let raceManager = RaceManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Dragon Boat Timer"
        setupUI()
        setupRaceManager()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        tableView.reloadData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground

        // Setup table view
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.showsVerticalScrollIndicator = false

        // Register cells
        tableView.register(HeaderCell.self, forCellReuseIdentifier: "HeaderCell")
        tableView.register(NewRaceButtonCell.self, forCellReuseIdentifier: "NewRaceButtonCell")
        tableView.register(CurrentRaceCell.self, forCellReuseIdentifier: "CurrentRaceCell")
        tableView.register(RaceHistoryCell.self, forCellReuseIdentifier: "RaceHistoryCell")
        tableView.register(EmptyStateCell.self, forCellReuseIdentifier: "EmptyStateCell")

        view.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }
    

    
    private func setupRaceManager() {
        raceManager.delegate = self
    }
    
    // MARK: - Actions

    @objc private func createNewRacePressed() {
        let createRaceVC = CreateRaceViewController()
        navigationController?.pushViewController(createRaceVC, animated: true)
    }

    @objc private func continueRacePressed() {
        guard let currentRace = raceManager.currentRace else { return }

        if currentRace.status == .created {
            let createRaceVC = CreateRaceViewController(existingRace: currentRace)
            navigationController?.pushViewController(createRaceVC, animated: true)
        } else if currentRace.status == .inProgress {
            let timerVC = RaceTimerViewController(race: currentRace)
            navigationController?.pushViewController(timerVC, animated: true)
        }
    }
}

// MARK: - RaceManagerDelegate

extension MainViewController: RaceManagerDelegate {
    func raceManager(_ manager: RaceManager, didUpdateRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.tableView.reloadData()
        }
    }

    func raceManager(_ manager: RaceManager, didFinishTeam team: Team, in race: RaceRecord) {
        // Handle team finish if needed
    }

    func raceManager(_ manager: RaceManager, didCompleteRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.tableView.reloadData()
        }
    }
}

// MARK: - UITableViewDataSource

extension MainViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 3 // Header, Actions, History
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
        case 0: return 1 // Header
        case 1: // Actions
            let hasCurrentRace = raceManager.currentRace != nil && raceManager.currentRace?.status != .completed
            return hasCurrentRace ? 2 : 1 // New Race + Current Race (if exists)
        case 2: // History
            let completedRaces = raceManager.completedRaces
            return completedRaces.isEmpty ? 1 : completedRaces.count // Empty state or race list
        default: return 0
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
        case 0: // Header
            let cell = tableView.dequeueReusableCell(withIdentifier: "HeaderCell", for: indexPath) as! HeaderCell
            return cell

        case 1: // Actions
            if indexPath.row == 0 {
                let cell = tableView.dequeueReusableCell(withIdentifier: "NewRaceButtonCell", for: indexPath) as! NewRaceButtonCell
                cell.delegate = self
                return cell
            } else {
                let cell = tableView.dequeueReusableCell(withIdentifier: "CurrentRaceCell", for: indexPath) as! CurrentRaceCell
                if let currentRace = raceManager.currentRace {
                    cell.configure(with: currentRace)
                }
                cell.delegate = self
                return cell
            }

        case 2: // History
            let completedRaces = raceManager.completedRaces
            if completedRaces.isEmpty {
                let cell = tableView.dequeueReusableCell(withIdentifier: "EmptyStateCell", for: indexPath) as! EmptyStateCell
                return cell
            } else {
                let cell = tableView.dequeueReusableCell(withIdentifier: "RaceHistoryCell", for: indexPath) as! RaceHistoryCell
                let race = completedRaces[indexPath.row]
                cell.configure(with: race)
                return cell
            }

        default:
            return UITableViewCell()
        }
    }
}

// MARK: - UITableViewDelegate

extension MainViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
        case 0: return 120 // Header
        case 1: return indexPath.row == 0 ? 80 : 100 // New Race button / Current Race card
        case 2: return raceManager.completedRaces.isEmpty ? 120 : 80 // Empty state / Race history
        default: return 80
        }
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        switch section {
        case 2: return raceManager.completedRaces.isEmpty ? nil : "Race History"
        default: return nil
        }
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        if indexPath.section == 2 && !raceManager.completedRaces.isEmpty {
            let race = raceManager.completedRaces[indexPath.row]
            let resultsVC = RaceResultsViewController(race: race)
            navigationController?.pushViewController(resultsVC, animated: true)
        }
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete && indexPath.section == 2 && !raceManager.completedRaces.isEmpty {
            let race = raceManager.completedRaces[indexPath.row]

            let alert = UIAlertController(title: "Delete Race",
                                        message: "Are you sure you want to delete this race record?",
                                        preferredStyle: .alert)

            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                self.raceManager.deleteRace(race)
                tableView.reloadData()
            })

            present(alert, animated: true)
        }
    }
}

// MARK: - RaceHistoryCell

class RaceHistoryCell: UITableViewCell {

    private let containerView = UIView()
    private let nameLabel = UILabel()
    private let dateLabel = UILabel()
    private let teamsLabel = UILabel()
    private let winnerLabel = UILabel()
    private let chevronImageView = UIImageView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(containerView)

        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        nameLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(nameLabel)

        dateLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        dateLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(dateLabel)

        teamsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        teamsLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(teamsLabel)

        winnerLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        winnerLabel.textColor = UIColor.dragonAccentSecondary
        containerView.addSubview(winnerLabel)

        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        containerView.addSubview(chevronImageView)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 20, bottom: 4, right: 20))
        }

        nameLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(12)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }

        dateLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.leading.equalToSuperview().offset(12)
        }

        teamsLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.leading.equalToSuperview().offset(12)
        }

        winnerLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }

        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-12)
            make.width.height.equalTo(16)
        }
    }

    func configure(with race: RaceRecord) {
        nameLabel.text = race.name
        dateLabel.text = TimeFormatter.formatRaceDate(race.createdAt)
        teamsLabel.text = "\(race.teams.count) teams"

        if let winner = race.winner {
            winnerLabel.text = "🏆 \(winner.name)"
        } else {
            winnerLabel.text = "No winner"
        }
    }
}

// MARK: - Cell Delegates

protocol NewRaceButtonCellDelegate: AnyObject {
    func newRaceButtonPressed()
}

protocol CurrentRaceCellDelegate: AnyObject {
    func currentRaceContinuePressed()
}

extension MainViewController: NewRaceButtonCellDelegate {
    func newRaceButtonPressed() {
        createNewRacePressed()
    }
}

extension MainViewController: CurrentRaceCellDelegate {
    func currentRaceContinuePressed() {
        continueRacePressed()
    }
}

// MARK: - Header Cell

class HeaderCell: UITableViewCell {
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        titleLabel.text = "Dragon Boat Timer"
        titleLabel.font = UIFont.systemFont(ofSize: 32, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)

        subtitleLabel.text = "Professional Race Recording System"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        contentView.addSubview(subtitleLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
}

// MARK: - New Race Button Cell

class NewRaceButtonCell: UITableViewCell {
    private let newRaceButton = UIButton(type: .system)
    weak var delegate: NewRaceButtonCellDelegate?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        newRaceButton.setTitle("Create New Race", for: .normal)
        newRaceButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        newRaceButton.setTitleColor(.white, for: .normal)
        newRaceButton.backgroundColor = UIColor.dragonAccent
        newRaceButton.layer.cornerRadius = 12
        newRaceButton.addTarget(self, action: #selector(buttonPressed), for: .touchUpInside)
        contentView.addSubview(newRaceButton)

        newRaceButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(10)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
        }
    }

    @objc private func buttonPressed() {
        delegate?.newRaceButtonPressed()
    }
}

// MARK: - Current Race Cell

class CurrentRaceCell: UITableViewCell {
    private let containerView = UIView()
    private let raceLabel = UILabel()
    private let statusLabel = UILabel()
    private let continueButton = UIButton(type: .system)
    weak var delegate: CurrentRaceCellDelegate?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(containerView)

        raceLabel.text = "Current Race"
        raceLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        raceLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(raceLabel)

        statusLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        statusLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(statusLabel)

        continueButton.setTitle("Continue", for: .normal)
        continueButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        continueButton.setTitleColor(.white, for: .normal)
        continueButton.backgroundColor = UIColor.dragonAccentSecondary
        continueButton.layer.cornerRadius = 8
        continueButton.addTarget(self, action: #selector(continuePressed), for: .touchUpInside)
        containerView.addSubview(continueButton)

        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 5, left: 20, bottom: 5, right: 20))
        }

        raceLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(raceLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(continueButton.snp.leading).offset(-16)
        }

        continueButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(80)
            make.height.equalTo(36)
        }
    }

    @objc private func continuePressed() {
        delegate?.currentRaceContinuePressed()
    }

    func configure(with race: RaceRecord) {
        raceLabel.text = race.name

        let statusText: String
        let buttonTitle: String

        switch race.status {
        case .created:
            statusText = "Ready to start • \(race.teams.count) teams"
            buttonTitle = "Setup"
        case .inProgress:
            let finishedCount = race.finishedTeamsCount
            let totalCount = race.teams.count
            statusText = "In progress • \(finishedCount)/\(totalCount) finished"
            buttonTitle = "Continue"
        case .completed:
            statusText = "Completed"
            buttonTitle = "View"
        }

        statusLabel.text = statusText
        continueButton.setTitle(buttonTitle, for: .normal)
    }
}

// MARK: - Empty State Cell

class EmptyStateCell: UITableViewCell {
    private let emptyLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        emptyLabel.text = "No race history yet.\nCreate your first race to get started!"
        emptyLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        emptyLabel.textColor = UIColor.dragonTextTertiary
        emptyLabel.textAlignment = .center
        emptyLabel.numberOfLines = 0
        contentView.addSubview(emptyLabel)

        emptyLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
}
