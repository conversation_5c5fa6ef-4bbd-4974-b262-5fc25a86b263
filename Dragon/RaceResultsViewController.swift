//
//  RaceResultsViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class RaceResultsViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let raceInfoCard = UIView()
    private let raceNameLabel = UILabel()
    private let raceDateLabel = UILabel()
    private let raceDurationLabel = UILabel()
    
    private let resultsLabel = UILabel()
    private let resultsTableView = UITableView()
    
    private let shareButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private let race: RaceRecord
    
    // MARK: - Initialization
    
    init(race: RaceRecord) {
        self.race = race
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        updateUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Race Results"
        
        // Navigation
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .action,
            target: self,
            action: #selector(sharePressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "🏁 Race Results"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        // Setup race info card
        raceInfoCard.backgroundColor = UIColor.dragonCardBackground
        raceInfoCard.layer.cornerRadius = 12
        raceInfoCard.layer.borderWidth = 1
        raceInfoCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(raceInfoCard)
        
        raceNameLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        raceNameLabel.textColor = UIColor.dragonTextPrimary
        raceNameLabel.numberOfLines = 0
        raceInfoCard.addSubview(raceNameLabel)
        
        raceDateLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        raceDateLabel.textColor = UIColor.dragonTextSecondary
        raceInfoCard.addSubview(raceDateLabel)
        
        raceDurationLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        raceDurationLabel.textColor = UIColor.dragonAccentSecondary
        raceInfoCard.addSubview(raceDurationLabel)
        
        // Setup results section
        resultsLabel.text = "Final Rankings"
        resultsLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        resultsLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(resultsLabel)
        
        resultsTableView.backgroundColor = .clear
        resultsTableView.separatorStyle = .none
        resultsTableView.isScrollEnabled = false
        resultsTableView.delegate = self
        resultsTableView.dataSource = self
        resultsTableView.register(ResultCell.self, forCellReuseIdentifier: "ResultCell")
        contentView.addSubview(resultsTableView)
        
        // Setup share button
        shareButton.setTitle("Share Results", for: .normal)
        shareButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        shareButton.setTitleColor(.white, for: .normal)
        shareButton.backgroundColor = UIColor.dragonAccent
        shareButton.layer.cornerRadius = 12
        shareButton.addTarget(self, action: #selector(sharePressed), for: .touchUpInside)
        contentView.addSubview(shareButton)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        raceInfoCard.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        raceNameLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        raceDateLabel.snp.makeConstraints { make in
            make.top.equalTo(raceNameLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        raceDurationLabel.snp.makeConstraints { make in
            make.top.equalTo(raceDateLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        resultsLabel.snp.makeConstraints { make in
            make.top.equalTo(raceInfoCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        resultsTableView.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(race.teams.count * 70)
        }
        
        shareButton.snp.makeConstraints { make in
            make.top.equalTo(resultsTableView.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    // MARK: - Actions
    
    @objc private func sharePressed() {
        let resultsText = generateResultsText()
        let activityVC = UIActivityViewController(activityItems: [resultsText], applicationActivities: nil)
        
        if let popover = activityVC.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }
        
        present(activityVC, animated: true)
    }
    
    // MARK: - UI Updates
    
    private func updateUI() {
        raceNameLabel.text = race.name
        raceDateLabel.text = TimeFormatter.formatRaceDate(race.createdAt)
        raceDurationLabel.text = "Duration: \(race.formattedDuration)"
        
        resultsTableView.reloadData()
    }
    
    // MARK: - Helper Methods
    
    private func generateResultsText() -> String {
        var text = "🏁 \(race.name)\n"
        text += "📅 \(TimeFormatter.formatRaceDate(race.createdAt))\n"
        text += "⏱️ Duration: \(race.formattedDuration)\n\n"
        text += "🏆 Final Rankings:\n"
        
        let rankedTeams = race.rankedTeams
        for (index, team) in rankedTeams.enumerated() {
            let position = index + 1
            let medal = position <= 3 ? ["🥇", "🥈", "🥉"][position - 1] : "\(position)."
            let time = team.isFinished ? team.formattedFinishTime() : "DNF"
            text += "\(medal) \(team.name) - \(time)\n"
        }
        
        text += "\n🐉 Generated by Dragon Boat Timer"
        return text
    }
}

// MARK: - UITableViewDataSource

extension RaceResultsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return race.teams.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ResultCell", for: indexPath) as! ResultCell
        let team = race.rankedTeams[indexPath.row]
        cell.configure(with: team, position: indexPath.row + 1)
        return cell
    }
}

// MARK: - UITableViewDelegate

extension RaceResultsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
}

// MARK: - ResultCell

class ResultCell: UITableViewCell {

    private let containerView = UIView()
    private let positionLabel = UILabel()
    private let teamNameLabel = UILabel()
    private let timeLabel = UILabel()
    private let medalImageView = UIImageView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(containerView)

        positionLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        positionLabel.textAlignment = .center
        positionLabel.layer.cornerRadius = 20
        positionLabel.layer.masksToBounds = true
        containerView.addSubview(positionLabel)

        medalImageView.contentMode = .scaleAspectFit
        containerView.addSubview(medalImageView)

        teamNameLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        teamNameLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(teamNameLabel)

        timeLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 16, weight: .medium)
        timeLabel.textColor = UIColor.dragonTextSecondary
        timeLabel.textAlignment = .right
        containerView.addSubview(timeLabel)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 20, bottom: 4, right: 20))
        }

        positionLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        medalImageView.snp.makeConstraints { make in
            make.leading.equalTo(positionLabel.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }

        teamNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(medalImageView.snp.trailing).offset(12)
            make.centerY.equalToSuperview()
            make.trailing.lessThanOrEqualTo(timeLabel.snp.leading).offset(-12)
        }

        timeLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.greaterThanOrEqualTo(80)
        }
    }

    func configure(with team: Team, position: Int) {
        positionLabel.text = "\(position)"
        teamNameLabel.text = team.name
        timeLabel.text = team.isFinished ? team.formattedFinishTime() : "DNF"

        // Configure position styling
        switch position {
        case 1:
            positionLabel.backgroundColor = UIColor.dragonRankFirst
            positionLabel.textColor = .black
            medalImageView.image = UIImage(systemName: "crown.fill")
            medalImageView.tintColor = UIColor.dragonRankFirst
            medalImageView.isHidden = false
            containerView.layer.borderColor = UIColor.dragonRankFirst.cgColor

        case 2:
            positionLabel.backgroundColor = UIColor.dragonRankSecond
            positionLabel.textColor = .black
            medalImageView.image = UIImage(systemName: "medal.fill")
            medalImageView.tintColor = UIColor.dragonRankSecond
            medalImageView.isHidden = false
            containerView.layer.borderColor = UIColor.dragonRankSecond.cgColor

        case 3:
            positionLabel.backgroundColor = UIColor.dragonRankThird
            positionLabel.textColor = .white
            medalImageView.image = UIImage(systemName: "medal.fill")
            medalImageView.tintColor = UIColor.dragonRankThird
            medalImageView.isHidden = false
            containerView.layer.borderColor = UIColor.dragonRankThird.cgColor

        default:
            positionLabel.backgroundColor = UIColor.dragonSeparator
            positionLabel.textColor = UIColor.dragonTextPrimary
            medalImageView.isHidden = true
            containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        }

        // Style for DNF teams
        if !team.isFinished {
            timeLabel.textColor = UIColor.dragonError
            timeLabel.text = "DNF"
        } else {
            timeLabel.textColor = UIColor.dragonTextSecondary
        }
    }
}
