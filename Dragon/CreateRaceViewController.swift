//
//  CreateRaceViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class CreateRaceViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let raceNameTextField = UITextField()
    private let teamsLabel = UILabel()
    private let teamsStackView = UIStackView()
    private let addTeamButton = UIButton(type: .system)
    private let startRaceButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private var teamInputViews: [TeamInputView] = []
    private let raceManager = RaceManager.shared
    private var existingRace: RaceRecord?
    
    // MARK: - Initialization
    
    init(existingRace: RaceRecord? = nil) {
        self.existingRace = existingRace
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupInitialTeams()
        updateStartButton()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        
        // Navigation
        title = existingRace != nil ? "Edit Race" : "Create Race"
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelPressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.keyboardDismissMode = .onDrag
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = existingRace != nil ? "Edit Race Details" : "Create New Race"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        // Setup race name input
        raceNameTextField.placeholder = "Race Name (Optional)"
        raceNameTextField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        raceNameTextField.textColor = UIColor.dragonTextPrimary
        raceNameTextField.backgroundColor = UIColor.dragonCardBackground
        raceNameTextField.layer.cornerRadius = 8
        raceNameTextField.layer.borderWidth = 1
        raceNameTextField.layer.borderColor = UIColor.dragonBorder.cgColor
        raceNameTextField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        raceNameTextField.leftViewMode = .always
        raceNameTextField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        raceNameTextField.rightViewMode = .always
        raceNameTextField.attributedPlaceholder = NSAttributedString(
            string: "Race Name (Optional)",
            attributes: [.foregroundColor: UIColor.dragonTextTertiary]
        )
        contentView.addSubview(raceNameTextField)
        
        // Setup teams section
        teamsLabel.text = "Teams (2-10 teams)"
        teamsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        teamsLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(teamsLabel)
        
        teamsStackView.axis = .vertical
        teamsStackView.spacing = 12
        teamsStackView.distribution = .fill
        contentView.addSubview(teamsStackView)
        
        // Setup add team button
        addTeamButton.setTitle("+ Add Team", for: .normal)
        addTeamButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        addTeamButton.setTitleColor(UIColor.dragonAccent, for: .normal)
        addTeamButton.backgroundColor = UIColor.dragonCardBackground
        addTeamButton.layer.cornerRadius = 8
        addTeamButton.layer.borderWidth = 1
        addTeamButton.layer.borderColor = UIColor.dragonAccent.cgColor
        addTeamButton.addTarget(self, action: #selector(addTeamPressed), for: .touchUpInside)
        contentView.addSubview(addTeamButton)
        
        // Setup start race button
        let buttonTitle = existingRace?.status == .created ? "Start Race" : "Update Race"
        startRaceButton.setTitle(buttonTitle, for: .normal)
        startRaceButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        startRaceButton.setTitleColor(.white, for: .normal)
        startRaceButton.backgroundColor = UIColor.dragonAccent
        startRaceButton.layer.cornerRadius = 12
        startRaceButton.addTarget(self, action: #selector(startRacePressed), for: .touchUpInside)
        contentView.addSubview(startRaceButton)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        raceNameTextField.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        teamsLabel.snp.makeConstraints { make in
            make.top.equalTo(raceNameTextField.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        teamsStackView.snp.makeConstraints { make in
            make.top.equalTo(teamsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        addTeamButton.snp.makeConstraints { make in
            make.top.equalTo(teamsStackView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        startRaceButton.snp.makeConstraints { make in
            make.top.equalTo(addTeamButton.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupInitialTeams() {
        if let existingRace = existingRace {
            // Load existing race data
            raceNameTextField.text = existingRace.name
            
            for team in existingRace.teams {
                addTeamInputView(with: team.name)
            }
        } else {
            // Add default two teams
            addTeamInputView(with: "")
            addTeamInputView(with: "")
        }
    }
    
    // MARK: - Actions
    
    @objc private func cancelPressed() {
        dismiss(animated: true)
    }
    
    @objc private func addTeamPressed() {
        guard teamInputViews.count < 10 else {
            showAlert(title: "Maximum Teams", message: "You can add up to 10 teams maximum.")
            return
        }
        
        addTeamInputView(with: "")
        updateAddTeamButton()
    }
    
    @objc private func startRacePressed() {
        let teamNames = getValidTeamNames()
        
        guard teamNames.count >= 2 else {
            showAlert(title: "Insufficient Teams", message: "Please add at least 2 teams to start the race.")
            return
        }
        
        let raceName = raceNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalRaceName = raceName?.isEmpty == false ? raceName : nil
        
        if let existingRace = existingRace {
            // Update existing race
            existingRace.name = finalRaceName ?? existingRace.name
            existingRace.teams = teamNames.map { Team(name: $0) }
            
            if existingRace.status == .created {
                // Start the race
                raceManager.startCurrentRace()
                let timerVC = RaceTimerViewController(race: existingRace)
                navigationController?.pushViewController(timerVC, animated: true)
            }
        } else {
            // Create new race
            let race = raceManager.createRace(name: finalRaceName, teamNames: teamNames)
            raceManager.startCurrentRace()
            
            let timerVC = RaceTimerViewController(race: race)
            navigationController?.pushViewController(timerVC, animated: true)
        }
        
        dismiss(animated: true)
    }
    
    // MARK: - Helper Methods
    
    private func addTeamInputView(with name: String) {
        let teamInputView = TeamInputView()
        teamInputView.teamName = name
        teamInputView.delegate = self
        teamInputViews.append(teamInputView)
        teamsStackView.addArrangedSubview(teamInputView)
        
        updateAddTeamButton()
    }
    
    private func removeTeamInputView(_ teamInputView: TeamInputView) {
        guard let index = teamInputViews.firstIndex(of: teamInputView) else { return }
        
        teamInputViews.remove(at: index)
        teamsStackView.removeArrangedSubview(teamInputView)
        teamInputView.removeFromSuperview()
        
        updateAddTeamButton()
        updateStartButton()
    }
    
    private func getValidTeamNames() -> [String] {
        return teamInputViews.compactMap { inputView in
            let name = inputView.teamName.trimmingCharacters(in: .whitespacesAndNewlines)
            return name.isEmpty ? nil : name
        }
    }
    
    private func updateAddTeamButton() {
        addTeamButton.isEnabled = teamInputViews.count < 10
        addTeamButton.alpha = addTeamButton.isEnabled ? 1.0 : 0.5
    }
    
    private func updateStartButton() {
        let validTeamCount = getValidTeamNames().count
        startRaceButton.isEnabled = validTeamCount >= 2
        startRaceButton.alpha = startRaceButton.isEnabled ? 1.0 : 0.5
        
        let buttonText = existingRace?.status == .created ? "Start Race" : "Update Race"
        startRaceButton.setTitle("\(buttonText) (\(validTeamCount) teams)", for: .normal)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - TeamInputViewDelegate

extension CreateRaceViewController: TeamInputViewDelegate {
    func teamInputViewDidChange(_ teamInputView: TeamInputView) {
        updateStartButton()
    }

    func teamInputViewDidRequestRemoval(_ teamInputView: TeamInputView) {
        guard teamInputViews.count > 2 else {
            showAlert(title: "Minimum Teams", message: "You need at least 2 teams for a race.")
            return
        }

        removeTeamInputView(teamInputView)
    }
}
