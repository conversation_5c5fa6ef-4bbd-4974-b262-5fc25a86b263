//
//  UIColor+Theme.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit

extension UIColor {
    
    // MARK: - Dark Theme Colors
    
    /// Primary dark background color - Deep charcoal
    static let dragonDarkBackground = UIColor(red: 0.12, green: 0.12, blue: 0.12, alpha: 1.0) // #1E1E1E
    
    /// Secondary background color - Slightly lighter charcoal
    static let dragonSecondaryBackground = UIColor(red: 0.16, green: 0.16, blue: 0.16, alpha: 1.0) // #292929
    
    /// Card/container background color
    static let dragonCardBackground = UIColor(red: 0.20, green: 0.20, blue: 0.20, alpha: 1.0) // #333333
    
    /// Primary text color - Light gray
    static let dragonTextPrimary = UIColor(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0) // #F2F2F2
    
    /// Secondary text color - Medium gray
    static let dragonTextSecondary = UIColor(red: 0.70, green: 0.70, blue: 0.70, alpha: 1.0) // #B3B3B3
    
    /// Tertiary text color - Darker gray
    static let dragonTextTertiary = UIColor(red: 0.50, green: 0.50, blue: 0.50, alpha: 1.0) // #808080
    
    /// Accent color - Dragon boat inspired red-orange
    static let dragonAccent = UIColor(red: 0.90, green: 0.35, blue: 0.20, alpha: 1.0) // #E65A33
    
    /// Secondary accent color - Golden yellow
    static let dragonAccentSecondary = UIColor(red: 0.95, green: 0.75, blue: 0.20, alpha: 1.0) // #F2BF33
    
    /// Success color - Green
    static let dragonSuccess = UIColor(red: 0.20, green: 0.70, blue: 0.30, alpha: 1.0) // #33B34D
    
    /// Warning color - Orange
    static let dragonWarning = UIColor(red: 0.95, green: 0.60, blue: 0.20, alpha: 1.0) // #F29933
    
    /// Error color - Red
    static let dragonError = UIColor(red: 0.85, green: 0.25, blue: 0.25, alpha: 1.0) // #D94040
    
    /// Border color - Subtle gray
    static let dragonBorder = UIColor(red: 0.30, green: 0.30, blue: 0.30, alpha: 1.0) // #4D4D4D
    
    /// Separator color - Very subtle gray
    static let dragonSeparator = UIColor(red: 0.25, green: 0.25, blue: 0.25, alpha: 1.0) // #404040
    
    // MARK: - Timer Specific Colors
    
    /// Timer active color - Bright blue
    static let dragonTimerActive = UIColor(red: 0.20, green: 0.60, blue: 0.95, alpha: 1.0) // #3399F2
    
    /// Timer finished color - Success green
    static let dragonTimerFinished = dragonSuccess
    
    /// Timer waiting color - Muted gray
    static let dragonTimerWaiting = UIColor(red: 0.40, green: 0.40, blue: 0.40, alpha: 1.0) // #666666
    
    // MARK: - Ranking Colors
    
    /// First place - Gold
    static let dragonRankFirst = UIColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0) // #FFD700
    
    /// Second place - Silver
    static let dragonRankSecond = UIColor(red: 0.75, green: 0.75, blue: 0.75, alpha: 1.0) // #C0C0C0
    
    /// Third place - Bronze
    static let dragonRankThird = UIColor(red: 0.80, green: 0.50, blue: 0.20, alpha: 1.0) // #CD7F32
    
    // MARK: - Convenience Methods
    
    /// Creates a color with alpha adjustment
    func withAlphaComponent(_ alpha: CGFloat) -> UIColor {
        return self.withAlphaComponent(alpha)
    }
    
    /// Creates a lighter version of the color
    func lighter(by percentage: CGFloat = 0.2) -> UIColor {
        return self.adjustBrightness(by: abs(percentage))
    }
    
    /// Creates a darker version of the color
    func darker(by percentage: CGFloat = 0.2) -> UIColor {
        return self.adjustBrightness(by: -abs(percentage))
    }
    
    private func adjustBrightness(by percentage: CGFloat) -> UIColor {
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        
        if self.getHue(&hue, saturation: &saturation, brightness: &brightness, alpha: &alpha) {
            brightness = max(min(brightness + percentage, 1.0), 0.0)
            return UIColor(hue: hue, saturation: saturation, brightness: brightness, alpha: alpha)
        }
        
        return self
    }
}
