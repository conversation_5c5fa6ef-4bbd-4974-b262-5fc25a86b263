//
//  RaceAnalysisViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class RaceAnalysisViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // Filter section
    private let filterCard = UIView()
    private let filterLabel = UILabel()
    private let teamFilterButton = UIButton(type: .system)
    private let dateFilterButton = UIButton(type: .system)
    private let clearFiltersButton = UIButton(type: .system)
    
    // Statistics cards
    private let overviewCard = UIView()
    private let teamAnalysisCard = UIView()
    private let trendsCard = UIView()
    
    // MARK: - Properties
    
    private let raceManager = RaceManager.shared
    private var filteredRaces: [RaceRecord] = []
    private var selectedTeamFilter: String?
    private var selectedDateRange: DateRange?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Race Analysis"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Race Records Analysis"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Analyze historical performance trends and team statistics"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupFilterCard()
        setupStatisticsCards()
    }
    
    private func setupFilterCard() {
        filterCard.backgroundColor = UIColor.dragonCardBackground
        filterCard.layer.cornerRadius = 12
        filterCard.layer.borderWidth = 1
        filterCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(filterCard)
        
        filterLabel.text = "Filters"
        filterLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        filterLabel.textColor = UIColor.dragonTextPrimary
        filterCard.addSubview(filterLabel)
        
        // Team filter button
        teamFilterButton.setTitle("All Teams", for: .normal)
        teamFilterButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        teamFilterButton.setTitleColor(UIColor.dragonAccent, for: .normal)
        teamFilterButton.backgroundColor = UIColor.dragonSecondaryBackground
        teamFilterButton.layer.cornerRadius = 8
        teamFilterButton.layer.borderWidth = 1
        teamFilterButton.layer.borderColor = UIColor.dragonAccent.cgColor
        teamFilterButton.addTarget(self, action: #selector(teamFilterPressed), for: .touchUpInside)
        filterCard.addSubview(teamFilterButton)
        
        // Date filter button
        dateFilterButton.setTitle("All Time", for: .normal)
        dateFilterButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        dateFilterButton.setTitleColor(UIColor.dragonAccent, for: .normal)
        dateFilterButton.backgroundColor = UIColor.dragonSecondaryBackground
        dateFilterButton.layer.cornerRadius = 8
        dateFilterButton.layer.borderWidth = 1
        dateFilterButton.layer.borderColor = UIColor.dragonAccent.cgColor
        dateFilterButton.addTarget(self, action: #selector(dateFilterPressed), for: .touchUpInside)
        filterCard.addSubview(dateFilterButton)
        
        // Clear filters button
        clearFiltersButton.setTitle("Clear", for: .normal)
        clearFiltersButton.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        clearFiltersButton.setTitleColor(UIColor.dragonTextSecondary, for: .normal)
        clearFiltersButton.addTarget(self, action: #selector(clearFiltersPressed), for: .touchUpInside)
        filterCard.addSubview(clearFiltersButton)
    }
    
    private func setupStatisticsCards() {
        // Overview card
        setupCard(overviewCard, title: "Overview Statistics")
        
        // Team analysis card
        setupCard(teamAnalysisCard, title: "Team Performance Analysis")
        
        // Trends card
        setupCard(trendsCard, title: "Performance Trends")
    }
    
    private func setupCard(_ card: UIView, title: String) {
        card.backgroundColor = UIColor.dragonCardBackground
        card.layer.cornerRadius = 12
        card.layer.borderWidth = 1
        card.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(card)
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        card.addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
            make.trailing.equalToSuperview().offset(-16)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        filterCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(120)
        }
        
        filterLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        teamFilterButton.snp.makeConstraints { make in
            make.top.equalTo(filterLabel.snp.bottom).offset(12)
            make.leading.equalToSuperview().offset(16)
            make.width.equalTo(120)
            make.height.equalTo(36)
        }
        
        dateFilterButton.snp.makeConstraints { make in
            make.top.equalTo(filterLabel.snp.bottom).offset(12)
            make.leading.equalTo(teamFilterButton.snp.trailing).offset(12)
            make.width.equalTo(120)
            make.height.equalTo(36)
        }
        
        clearFiltersButton.snp.makeConstraints { make in
            make.top.equalTo(filterLabel.snp.bottom).offset(12)
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(60)
            make.height.equalTo(36)
        }
        
        overviewCard.snp.makeConstraints { make in
            make.top.equalTo(filterCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(200)
        }
        
        teamAnalysisCard.snp.makeConstraints { make in
            make.top.equalTo(overviewCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(300)
        }
        
        trendsCard.snp.makeConstraints { make in
            make.top.equalTo(teamAnalysisCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(250)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    // MARK: - Data Loading
    
    private func loadData() {
        filteredRaces = raceManager.completedRaces
        updateUI()
    }
    
    private func refreshData() {
        applyFilters()
        updateUI()
    }
    
    private func applyFilters() {
        var races = raceManager.completedRaces
        
        // Apply team filter
        if let teamFilter = selectedTeamFilter {
            races = races.filter { race in
                race.teams.contains { $0.name.lowercased().contains(teamFilter.lowercased()) }
            }
        }
        
        // Apply date filter
        if let dateRange = selectedDateRange {
            races = races.filter { race in
                dateRange.contains(race.createdAt)
            }
        }
        
        filteredRaces = races
    }
    
    private func updateUI() {
        updateOverviewCard()
        updateTeamAnalysisCard()
        updateTrendsCard()
    }

    // MARK: - UI Updates

    private func updateOverviewCard() {
        // Clear existing content
        overviewCard.subviews.forEach { view in
            if view != overviewCard.subviews.first { // Keep title label
                view.removeFromSuperview()
            }
        }

        let titleLabel = overviewCard.subviews.first as! UILabel

        // Create statistics
        let totalRaces = filteredRaces.count
        let totalTeams = Set(filteredRaces.flatMap { $0.teams.map { $0.name } }).count
        let averageRaceTime = calculateAverageRaceTime()
        let fastestTime = calculateFastestTime()

        let statsStackView = UIStackView()
        statsStackView.axis = .vertical
        statsStackView.spacing = 8
        statsStackView.distribution = .fillEqually
        overviewCard.addSubview(statsStackView)

        // Add statistics
        statsStackView.addArrangedSubview(createStatRow("Total Races:", "\(totalRaces)"))
        statsStackView.addArrangedSubview(createStatRow("Unique Teams:", "\(totalTeams)"))
        if let avgTime = averageRaceTime {
            statsStackView.addArrangedSubview(createStatRow("Average Time:", TimeFormatter.formatTime(avgTime)))
        }
        if let fastTime = fastestTime {
            statsStackView.addArrangedSubview(createStatRow("Fastest Time:", TimeFormatter.formatTime(fastTime)))
        }

        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }
    }

    private func updateTeamAnalysisCard() {
        // Clear existing content
        teamAnalysisCard.subviews.forEach { view in
            if view != teamAnalysisCard.subviews.first { // Keep title label
                view.removeFromSuperview()
            }
        }

        let titleLabel = teamAnalysisCard.subviews.first as! UILabel

        // Get team statistics
        let teamStats = calculateTeamStatistics()

        if teamStats.isEmpty {
            let noDataLabel = UILabel()
            noDataLabel.text = "No team data available"
            noDataLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
            noDataLabel.textColor = UIColor.dragonTextTertiary
            noDataLabel.textAlignment = .center
            teamAnalysisCard.addSubview(noDataLabel)

            noDataLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
            return
        }

        // Create table view for team stats
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.isScrollEnabled = false
        teamAnalysisCard.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }

        // Configure table view (simplified for now)
        // In a real implementation, you'd set up a proper data source
    }

    private func updateTrendsCard() {
        // Clear existing content
        trendsCard.subviews.forEach { view in
            if view != trendsCard.subviews.first { // Keep title label
                view.removeFromSuperview()
            }
        }

        let titleLabel = trendsCard.subviews.first as! UILabel

        // Add trends button
        let viewTrendsButton = UIButton(type: .system)
        viewTrendsButton.setTitle("View Performance Trends", for: .normal)
        viewTrendsButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        viewTrendsButton.setTitleColor(.white, for: .normal)
        viewTrendsButton.backgroundColor = UIColor.dragonAccent
        viewTrendsButton.layer.cornerRadius = 8
        viewTrendsButton.addTarget(self, action: #selector(viewTrendsPressed), for: .touchUpInside)
        trendsCard.addSubview(viewTrendsButton)

        viewTrendsButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(44)
        }

        // Add team comparison button
        let compareTeamsButton = UIButton(type: .system)
        compareTeamsButton.setTitle("Compare Teams", for: .normal)
        compareTeamsButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        compareTeamsButton.setTitleColor(.white, for: .normal)
        compareTeamsButton.backgroundColor = UIColor.dragonAccentSecondary
        compareTeamsButton.layer.cornerRadius = 8
        compareTeamsButton.addTarget(self, action: #selector(compareTeamsPressed), for: .touchUpInside)
        trendsCard.addSubview(compareTeamsButton)

        compareTeamsButton.snp.makeConstraints { make in
            make.top.equalTo(viewTrendsButton.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(44)
        }
    }

    // MARK: - Helper Methods

    private func createStatRow(_ label: String, _ value: String) -> UIView {
        let container = UIView()

        let labelView = UILabel()
        labelView.text = label
        labelView.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        labelView.textColor = UIColor.dragonTextSecondary
        container.addSubview(labelView)

        let valueView = UILabel()
        valueView.text = value
        valueView.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        valueView.textColor = UIColor.dragonTextPrimary
        valueView.textAlignment = .right
        container.addSubview(valueView)

        labelView.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
        }

        valueView.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
        }

        return container
    }

    private func calculateAverageRaceTime() -> TimeInterval? {
        let finishedTeams = filteredRaces.flatMap { $0.teams.filter { $0.isFinished } }
        guard !finishedTeams.isEmpty else { return nil }

        let totalTime = finishedTeams.compactMap { $0.finishTime }.reduce(0, +)
        return totalTime / Double(finishedTeams.count)
    }

    private func calculateFastestTime() -> TimeInterval? {
        let finishedTeams = filteredRaces.flatMap { $0.teams.filter { $0.isFinished } }
        return finishedTeams.compactMap { $0.finishTime }.min()
    }

    private func calculateTeamStatistics() -> [TeamStatistics] {
        var teamStats: [String: TeamStatistics] = [:]

        for race in filteredRaces {
            for team in race.teams {
                if teamStats[team.name] == nil {
                    teamStats[team.name] = TeamStatistics(name: team.name)
                }

                teamStats[team.name]?.addRace(team: team)
            }
        }

        return Array(teamStats.values).sorted { $0.averageTime ?? Double.infinity < $1.averageTime ?? Double.infinity }
    }

    // MARK: - Actions

    @objc private func teamFilterPressed() {
        showTeamFilterOptions()
    }

    @objc private func dateFilterPressed() {
        showDateFilterOptions()
    }

    @objc private func clearFiltersPressed() {
        selectedTeamFilter = nil
        selectedDateRange = nil
        teamFilterButton.setTitle("All Teams", for: .normal)
        dateFilterButton.setTitle("All Time", for: .normal)
        refreshData()
    }

    @objc private func viewTrendsPressed() {
        let trendsVC = PerformanceTrendsViewController(races: filteredRaces)
        navigationController?.pushViewController(trendsVC, animated: true)
    }

    @objc private func compareTeamsPressed() {
        let compareVC = TeamComparisonViewController(races: filteredRaces)
        navigationController?.pushViewController(compareVC, animated: true)
    }

    private func showTeamFilterOptions() {
        let allTeams = Set(raceManager.completedRaces.flatMap { $0.teams.map { $0.name } }).sorted()

        let alert = UIAlertController(title: "Filter by Team", message: "Select a team to filter by", preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "All Teams", style: .default) { _ in
            self.selectedTeamFilter = nil
            self.teamFilterButton.setTitle("All Teams", for: .normal)
            self.refreshData()
        })

        for team in allTeams {
            alert.addAction(UIAlertAction(title: team, style: .default) { _ in
                self.selectedTeamFilter = team
                self.teamFilterButton.setTitle(team, for: .normal)
                self.refreshData()
            })
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = teamFilterButton
            popover.sourceRect = teamFilterButton.bounds
        }

        present(alert, animated: true)
    }

    private func showDateFilterOptions() {
        let alert = UIAlertController(title: "Filter by Date", message: "Select a time period", preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "All Time", style: .default) { _ in
            self.selectedDateRange = nil
            self.dateFilterButton.setTitle("All Time", for: .normal)
            self.refreshData()
        })

        alert.addAction(UIAlertAction(title: "Last 7 Days", style: .default) { _ in
            self.selectedDateRange = DateRange.lastDays(7)
            self.dateFilterButton.setTitle("Last 7 Days", for: .normal)
            self.refreshData()
        })

        alert.addAction(UIAlertAction(title: "Last 30 Days", style: .default) { _ in
            self.selectedDateRange = DateRange.lastDays(30)
            self.dateFilterButton.setTitle("Last 30 Days", for: .normal)
            self.refreshData()
        })

        alert.addAction(UIAlertAction(title: "Last 3 Months", style: .default) { _ in
            self.selectedDateRange = DateRange.lastMonths(3)
            self.dateFilterButton.setTitle("Last 3 Months", for: .normal)
            self.refreshData()
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = dateFilterButton
            popover.sourceRect = dateFilterButton.bounds
        }

        present(alert, animated: true)
    }
}
