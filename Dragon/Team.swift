//
//  Team.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Represents a team participating in a dragon boat race
class Team: NSObject, Codable {
    
    // MARK: - Properties
    
    /// Unique identifier for the team
    let id: UUID
    
    /// Name of the team
    var name: String
    
    /// Finish time in seconds from race start (nil if not finished)
    var finishTime: TimeInterval?
    
    /// Whether the team has finished the race
    var isFinished: Bool {
        return finishTime != nil
    }
    
    /// Rank in the race (1-based, nil if not finished or not calculated)
    var rank: Int?
    
    // MARK: - Initialization
    
    /// Initialize a new team with a name
    /// - Parameter name: The team name
    init(name: String) {
        self.id = UUID()
        self.name = name
        self.finishTime = nil
        self.rank = nil
        super.init()
    }
    
    /// Initialize a team with all properties (for decoding)
    /// - Parameters:
    ///   - id: Unique identifier
    ///   - name: Team name
    ///   - finishTime: Finish time in seconds
    ///   - rank: Team rank
    init(id: UUID, name: String, finishTime: TimeInterval?, rank: Int?) {
        self.id = id
        self.name = name
        self.finishTime = finishTime
        self.rank = rank
        super.init()
    }
    
    // MARK: - Methods
    
    /// Mark the team as finished with the given time
    /// - Parameter time: Finish time in seconds from race start
    func finish(at time: TimeInterval) {
        self.finishTime = time
    }
    
    /// Reset the team's finish status
    func reset() {
        self.finishTime = nil
        self.rank = nil
    }
    
    /// Get formatted finish time string
    /// - Returns: Formatted time string (e.g., "1:23.45") or "Not Finished"
    func formattedFinishTime() -> String {
        guard let finishTime = finishTime else {
            return "Not Finished"
        }
        return TimeFormatter.formatTime(finishTime)
    }
    
    // MARK: - Codable
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case finishTime
        case rank
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        finishTime = try container.decodeIfPresent(TimeInterval.self, forKey: .finishTime)
        rank = try container.decodeIfPresent(Int.self, forKey: .rank)
        super.init()
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encodeIfPresent(finishTime, forKey: .finishTime)
        try container.encodeIfPresent(rank, forKey: .rank)
    }
}

// MARK: - Equatable

extension Team {
    override func isEqual(_ object: Any?) -> Bool {
        guard let other = object as? Team else { return false }
        return id == other.id
    }
    
    override var hash: Int {
        return id.hashValue
    }
}

// MARK: - Comparable

extension Team: Comparable {
    static func < (lhs: Team, rhs: Team) -> Bool {
        // Teams with finish times come before those without
        switch (lhs.finishTime, rhs.finishTime) {
        case (let lhsTime?, let rhsTime?):
            return lhsTime < rhsTime
        case (nil, _?):
            return false
        case (_?, nil):
            return true
        case (nil, nil):
            return lhs.name < rhs.name
        }
    }
}
