//
//  StrategyTemplatesViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategyTemplatesViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    private let templatesTableView = UITableView()
    
    // MARK: - Properties
    
    private let strategyManager = StrategyManager.shared
    private let templateTypes = StrategyTemplate.getAvailableTemplates()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Strategy Templates"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Strategy Templates"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Choose a proven strategy template to get started quickly"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        // Setup table view
        templatesTableView.backgroundColor = .clear
        templatesTableView.separatorStyle = .none
        templatesTableView.delegate = self
        templatesTableView.dataSource = self
        templatesTableView.register(TemplateCell.self, forCellReuseIdentifier: "TemplateCell")
        contentView.addSubview(templatesTableView)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        templatesTableView.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(templateTypes.count * 120)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
}

// MARK: - UITableViewDataSource

extension StrategyTemplatesViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return templateTypes.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "TemplateCell", for: indexPath) as! TemplateCell
        let templateType = templateTypes[indexPath.row]
        cell.configure(with: templateType)
        return cell
    }
}

// MARK: - UITableViewDelegate

extension StrategyTemplatesViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let templateType = templateTypes[indexPath.row]
        
        // Show distance selection alert
        showDistanceSelectionAlert(for: templateType)
    }
    
    private func showDistanceSelectionAlert(for templateType: StrategyType) {
        let alert = UIAlertController(title: "Select Race Distance", message: "Choose the race distance for your strategy", preferredStyle: .actionSheet)
        
        for distance in StrategyManager.commonDistances {
            let action = UIAlertAction(title: "\(Int(distance))m", style: .default) { _ in
                self.createStrategyFromTemplate(type: templateType, distance: distance)
            }
            alert.addAction(action)
        }
        
        alert.addAction(UIAlertAction(title: "Custom Distance", style: .default) { _ in
            self.showCustomDistanceAlert(for: templateType)
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = CGRect(x: view.bounds.midX, y: view.bounds.midY, width: 0, height: 0)
        }
        
        present(alert, animated: true)
    }
    
    private func showCustomDistanceAlert(for templateType: StrategyType) {
        let alert = UIAlertController(title: "Custom Distance", message: "Enter the race distance in meters", preferredStyle: .alert)
        
        alert.addTextField { textField in
            textField.placeholder = "Distance (meters)"
            textField.keyboardType = .numberPad
        }
        
        let createAction = UIAlertAction(title: "Create", style: .default) { _ in
            guard let text = alert.textFields?.first?.text,
                  let distance = Double(text), distance > 0 else {
                self.showAlert(title: "Invalid Distance", message: "Please enter a valid distance.")
                return
            }
            
            self.createStrategyFromTemplate(type: templateType, distance: distance)
        }
        
        alert.addAction(createAction)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        present(alert, animated: true)
    }
    
    private func createStrategyFromTemplate(type: StrategyType, distance: Double) {
        // Estimate target time based on distance
        let averageSpeed = 15.0 / 3.6 // 15 km/h in m/s
        let targetTime = distance / averageSpeed
        
        // Create strategy from template
        let strategy = StrategyTemplate.createStrategy(
            type: type,
            distance: distance,
            targetTime: targetTime
        )
        
        strategy.name = "\(type.displayName) - \(Int(distance))m"
        strategyManager.saveStrategy(strategy)
        strategyManager.setCurrentStrategy(strategy)
        
        // Navigate to calculator to edit
        let calculatorVC = PaceCalculatorViewController(strategy: strategy)
        navigationController?.pushViewController(calculatorVC, animated: true)
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - TemplateCell

class TemplateCell: UITableViewCell {
    
    private let containerView = UIView()
    private let titleLabel = UILabel()
    private let descriptionLabel = UILabel()
    private let iconImageView = UIImageView()
    private let chevronImageView = UIImageView()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(containerView)
        
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = UIColor.dragonAccent
        containerView.addSubview(iconImageView)
        
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(titleLabel)
        
        descriptionLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        descriptionLabel.textColor = UIColor.dragonTextSecondary
        descriptionLabel.numberOfLines = 0
        containerView.addSubview(descriptionLabel)
        
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        containerView.addSubview(chevronImageView)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 20, bottom: 8, right: 20))
        }
        
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(16)
            make.top.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-16)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    func configure(with strategyType: StrategyType) {
        titleLabel.text = strategyType.displayName
        descriptionLabel.text = strategyType.description
        
        let iconName: String
        switch strategyType {
        case .evenPace:
            iconName = "speedometer"
        case .startBurst:
            iconName = "bolt.fill"
        case .finishSprint:
            iconName = "flame.fill"
        case .negative:
            iconName = "chart.line.uptrend.xyaxis"
        case .positive:
            iconName = "chart.line.downtrend.xyaxis"
        case .custom:
            iconName = "slider.horizontal.3"
        }
        
        iconImageView.image = UIImage(systemName: iconName)
    }
}
