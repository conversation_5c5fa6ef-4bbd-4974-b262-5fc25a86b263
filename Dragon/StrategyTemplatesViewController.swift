//
//  StrategyTemplatesViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategyTemplatesViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // 参数输入卡片
    private let parametersCard = UIView()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    private let strokeDistanceTextField = UITextField()
    
    // 模板选择卡片
    private let templatesStackView = UIStackView()
    
    // MARK: - Properties
    
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupTemplates()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Strategy Templates"

        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)

        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)

        // Setup title
        titleLabel.text = "Choose Tactical Rhythm Template"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)

        subtitleLabel.text = "Select the appropriate race strategy template based on team characteristics"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupParametersCard()
        setupTemplatesView()
    }
    
    private func setupParametersCard() {
        parametersCard.backgroundColor = UIColor.dragonCardBackground
        parametersCard.layer.cornerRadius = 12
        parametersCard.layer.borderWidth = 1
        parametersCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(parametersCard)
        
        let parametersLabel = UILabel()
        parametersLabel.text = "Race Parameters"
        parametersLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        parametersLabel.textColor = UIColor.dragonTextPrimary
        parametersCard.addSubview(parametersLabel)

        // Distance input
        distanceTextField.placeholder = "Race Distance (meters)"
        distanceTextField.text = "500"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        parametersCard.addSubview(distanceTextField)

        // Target time input
        targetTimeTextField.placeholder = "Target Time (MM:SS)"
        targetTimeTextField.text = "2:00"
        setupTextField(targetTimeTextField)
        parametersCard.addSubview(targetTimeTextField)

        // Stroke distance input
        strokeDistanceTextField.placeholder = "Stroke Distance (meters)"
        strokeDistanceTextField.text = "2.5"
        strokeDistanceTextField.keyboardType = .decimalPad
        setupTextField(strokeDistanceTextField)
        parametersCard.addSubview(strokeDistanceTextField)
        
        // 约束
        parametersLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(parametersLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }
        
        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }
        
        strokeDistanceTextField.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupTemplatesView() {
        templatesStackView.axis = .vertical
        templatesStackView.spacing = 16
        templatesStackView.distribution = .fill
        contentView.addSubview(templatesStackView)
    }
    
    private func setupTemplates() {
        let templateTypes = StrategyTemplateGenerator.getAvailableTemplateTypes()
        
        for strategyType in templateTypes {
            let templateCard = createTemplateCard(for: strategyType)
            templatesStackView.addArrangedSubview(templateCard)
        }
    }
    
    private func createTemplateCard(for strategyType: StrategyType) -> UIView {
        let card = UIView()
        card.backgroundColor = UIColor.dragonCardBackground
        card.layer.cornerRadius = 12
        card.layer.borderWidth = 1
        card.layer.borderColor = UIColor.dragonBorder.cgColor
        
        // 图标
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: getIconForStrategyType(strategyType))
        iconImageView.tintColor = getColorForStrategyType(strategyType)
        iconImageView.contentMode = .scaleAspectFit
        card.addSubview(iconImageView)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = strategyType.displayName
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        card.addSubview(titleLabel)
        
        // 描述
        let descriptionLabel = UILabel()
        descriptionLabel.text = strategyType.description
        descriptionLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        descriptionLabel.textColor = UIColor.dragonTextSecondary
        descriptionLabel.numberOfLines = 0
        card.addSubview(descriptionLabel)
        
        // Select button
        let selectButton = UIButton(type: .system)
        selectButton.setTitle("Select", for: .normal)
        selectButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        selectButton.setTitleColor(.white, for: .normal)
        selectButton.backgroundColor = getColorForStrategyType(strategyType)
        selectButton.layer.cornerRadius = 8
        selectButton.tag = strategyType.hashValue
        selectButton.addTarget(self, action: #selector(templateSelected(_:)), for: .touchUpInside)
        card.addSubview(selectButton)
        
        // 约束
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(selectButton.snp.leading).offset(-16)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.trailing.lessThanOrEqualTo(selectButton.snp.leading).offset(-16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        selectButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(80)
            make.height.equalTo(36)
        }
        
        card.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(100)
        }
        
        return card
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonSecondaryBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        parametersCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        templatesStackView.snp.makeConstraints { make in
            make.top.equalTo(parametersCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    // MARK: - Actions
    
    @objc private func templateSelected(_ sender: UIButton) {
        guard let distanceText = distanceTextField.text,
              let distance = Double(distanceText),
              distance > 0 else {
            showAlert(title: "Invalid Distance", message: "Please enter a valid race distance")
            return
        }
        
        let targetTime = parseTime(targetTimeTextField.text ?? "2:00")
        let strokeDistance = Double(strokeDistanceTextField.text ?? "2.5") ?? 2.5
        
        // Find strategy type based on button tag
        let strategyTypes = StrategyTemplateGenerator.getAvailableTemplateTypes()
        guard let strategyType = strategyTypes.first(where: { $0.hashValue == sender.tag }) else { return }

        // Generate strategy
        let strategy = StrategyTemplateGenerator.createStrategy(
            type: strategyType,
            distance: distance,
            targetTime: targetTime,
            strokeDistance: strokeDistance
        )

        // Save and set as current strategy
        strategyManager.saveStrategy(strategy)
        strategyManager.setCurrentStrategy(strategy)

        showAlert(title: "Strategy Created", message: "\(strategyType.displayName) strategy generated and set as current strategy") {
            self.navigationController?.popViewController(animated: true)
        }
    }
    
    // MARK: - Helper Methods
    
    private func getIconForStrategyType(_ type: StrategyType) -> String {
        switch type {
        case .evenPace: return "speedometer"
        case .startBurst: return "bolt.fill"
        case .finishSprint: return "flame.fill"
        case .negative: return "chart.line.uptrend.xyaxis"
        case .positive: return "chart.line.downtrend.xyaxis"
        case .custom: return "gear"
        }
    }
    
    private func getColorForStrategyType(_ type: StrategyType) -> UIColor {
        switch type {
        case .evenPace: return UIColor.systemBlue
        case .startBurst: return UIColor.systemRed
        case .finishSprint: return UIColor.systemOrange
        case .negative: return UIColor.systemGreen
        case .positive: return UIColor.systemPurple
        case .custom: return UIColor.systemGray
        }
    }
    
    private func parseTime(_ timeString: String) -> TimeInterval {
        let components = timeString.components(separatedBy: ":")
        if components.count == 2,
           let minutes = Double(components[0]),
           let seconds = Double(components[1]) {
            return (minutes * 60) + seconds
        }
        return 120.0 // Default 2 minutes
    }
    
    private func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
}
