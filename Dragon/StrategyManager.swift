//
//  StrategyManager.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

// Strategy manager delegate
protocol StrategyManagerDelegate: AnyObject {
    func strategyManager(_ manager: StrategyManager, didUpdateStrategy strategy: RaceStrategy)
    func strategyManager(_ manager: Strategy<PERSON>ana<PERSON>, didDeleteStrategy strategy: RaceStrategy)
    func strategyManager(_ manager: StrategyManager, didSetCurrentStrategy strategy: RaceStrategy?)
}

// Performance prediction result
struct PerformancePrediction {
    let predictedTime: TimeInterval
    let confidence: Double // 0.0 - 1.0
    let recommendations: [String]
    let riskFactors: [String]
    
    var formattedTime: String {
        return TimeFormatter.formatTime(predictedTime)
    }
    
    var confidencePercentage: Int {
        return Int(confidence * 100)
    }
}

class StrategyManager: NSObject {
    static let shared = StrategyManager()
    
    weak var delegate: StrategyManagerDelegate?
    
    // Stored strategies
    private(set) var strategies: [RaceStrategy] = []

    // Currently selected strategy
    private(set) var currentStrategy: RaceStrategy?

    // User data storage keys
    private let strategiesKey = "SavedRaceStrategies"
    private let currentStrategyKey = "CurrentRaceStrategy"
    
    override init() {
        super.init()
        loadStrategies()
        loadCurrentStrategy()
    }
    
    // MARK: - Strategy Management

    // Save strategy
    func saveStrategy(_ strategy: RaceStrategy) {
        strategy.updateModificationTime()

        if let index = strategies.firstIndex(where: { $0.id == strategy.id }) {
            strategies[index] = strategy
        } else {
            strategies.append(strategy)
        }

        saveStrategies()
        delegate?.strategyManager(self, didUpdateStrategy: strategy)
    }

    // Delete strategy
    func deleteStrategy(_ strategy: RaceStrategy) {
        strategies.removeAll { $0.id == strategy.id }

        if currentStrategy?.id == strategy.id {
            currentStrategy = nil
            saveCurrentStrategy()
            delegate?.strategyManager(self, didSetCurrentStrategy: nil)
        }

        saveStrategies()
        delegate?.strategyManager(self, didDeleteStrategy: strategy)
    }

    // Set current strategy
    func setCurrentStrategy(_ strategy: RaceStrategy?) {
        currentStrategy = strategy
        saveCurrentStrategy()
        delegate?.strategyManager(self, didSetCurrentStrategy: strategy)
    }

    // Get custom strategies list
    var customStrategies: [RaceStrategy] {
        return strategies.sorted { $0.lastModified > $1.lastModified }
    }
    
    // MARK: - Performance Prediction

    // Predict performance based on historical data
    func predictPerformance(for team: Team, using strategy: RaceStrategy) -> PerformancePrediction {
        // Get team historical performance data
        let raceManager = RaceManager.shared
        let teamHistory = raceManager.completedRaces.compactMap { race in
            race.teams.first { $0.id == team.id }
        }

        var predictedTime = strategy.targetTime
        var confidence: Double = 0.5 // Base confidence
        var recommendations: [String] = []
        var riskFactors: [String] = []

        // If historical data exists, make more accurate predictions
        if !teamHistory.isEmpty {
            let averageTime = teamHistory.compactMap { $0.finishTime }.reduce(0, +) / Double(teamHistory.count)
            let bestTime = teamHistory.compactMap { $0.finishTime }.min() ?? strategy.targetTime

            // Adjust prediction based on historical performance
            let performanceFactor = bestTime / strategy.targetTime
            predictedTime = strategy.targetTime * performanceFactor

            // Increase confidence
            confidence = min(0.9, 0.5 + Double(teamHistory.count) * 0.1)

            // Generate recommendations
            if performanceFactor < 0.95 {
                recommendations.append("Team has excellent historical performance, consider more aggressive strategy")
            } else if performanceFactor > 1.1 {
                recommendations.append("Recommend conservative strategy to ensure race completion")
                riskFactors.append("Historical results suggest more training time may be needed")
            }
        }

        // Analyze strategy risks
        analyzeStrategyRisks(strategy: strategy, recommendations: &recommendations, riskFactors: &riskFactors)

        return PerformancePrediction(
            predictedTime: predictedTime,
            confidence: confidence,
            recommendations: recommendations,
            riskFactors: riskFactors
        )
    }
    
    // Analyze strategy risks
    private func analyzeStrategyRisks(strategy: RaceStrategy, recommendations: inout [String], riskFactors: inout [String]) {
        let maxStrokeRate = strategy.phases.map { $0.targetStrokeRate }.max() ?? 0
        let minStrokeRate = strategy.phases.map { $0.targetStrokeRate }.min() ?? 0
        let strokeRateVariation = maxStrokeRate - minStrokeRate

        // Stroke rate analysis
        if maxStrokeRate > 100 {
            riskFactors.append("Maximum stroke rate exceeds 100 SPM, may cause early energy depletion")
            recommendations.append("Consider reducing peak stroke rate and extending duration")
        }

        if strokeRateVariation > 20 {
            riskFactors.append("Large stroke rate variation requires good rhythm control")
            recommendations.append("Strengthen rhythm transition training for smooth transitions")
        }

        // Substitution timing analysis
        let substitutionCount = strategy.substitutionTimings.count
        if substitutionCount > 2 {
            riskFactors.append("Multiple substitutions may affect rhythm continuity")
        } else if substitutionCount == 0 && strategy.raceDistance > 1000 {
            recommendations.append("Long distance races should consider substitutions to maintain energy")
        }
        
        // 阶段分配分析
        let startPhase = strategy.phases.first { $0.phaseType == .start }
        if let start = startPhase, start.targetStrokeRate > strategy.averageStrokeRate + 15 {
            riskFactors.append("起步划频过高，可能影响后程表现")
        }
        
        // 策略类型特定建议
        switch strategy.strategyType {
        case .startBurst:
            recommendations.append("起步爆发型策略需要优秀的起步技术和体能储备")
        case .finishSprint:
            recommendations.append("冲刺终结型策略需要精确的体力分配和强大的冲刺能力")
        case .negative:
            recommendations.append("负分段策略考验心理素质，需要抵制前程跟风的诱惑")
        case .positive:
            recommendations.append("正分段策略需要强大的前程能力和良好的体能基础")
        default:
            break
        }
    }
    
    // MARK: - 数据持久化
    
    private func saveStrategies() {
        do {
            let data = try JSONEncoder().encode(strategies)
            UserDefaults.standard.set(data, forKey: strategiesKey)
        } catch {
            print("Failed to save strategies: \(error)")
        }
    }
    
    private func loadStrategies() {
        guard let data = UserDefaults.standard.data(forKey: strategiesKey) else { return }
        
        do {
            strategies = try JSONDecoder().decode([RaceStrategy].self, from: data)
        } catch {
            print("Failed to load strategies: \(error)")
            strategies = []
        }
    }
    
    private func saveCurrentStrategy() {
        if let strategy = currentStrategy {
            do {
                let data = try JSONEncoder().encode(strategy)
                UserDefaults.standard.set(data, forKey: currentStrategyKey)
            } catch {
                print("Failed to save current strategy: \(error)")
            }
        } else {
            UserDefaults.standard.removeObject(forKey: currentStrategyKey)
        }
    }
    
    private func loadCurrentStrategy() {
        guard let data = UserDefaults.standard.data(forKey: currentStrategyKey) else { return }
        
        do {
            currentStrategy = try JSONDecoder().decode(RaceStrategy.self, from: data)
        } catch {
            print("Failed to load current strategy: \(error)")
            currentStrategy = nil
        }
    }
    
    // MARK: - 工具方法
    
    // 创建新策略
    func createNewStrategy(name: String, distance: Double, targetTime: TimeInterval, type: StrategyType = .custom) -> RaceStrategy {
        let strategy = StrategyTemplateGenerator.createStrategy(
            type: type,
            distance: distance,
            targetTime: targetTime
        )
        strategy.name = name
        strategy.strategyType = type
        return strategy
    }
    
    // 复制策略
    func duplicateStrategy(_ strategy: RaceStrategy) -> RaceStrategy {
        let newStrategy = RaceStrategy(
            name: "\(strategy.name) - Copy",
            strategyType: strategy.strategyType,
            raceDistance: strategy.raceDistance,
            targetTime: strategy.targetTime
        )
        
        // 复制所有阶段
        for phase in strategy.phases {
            let newPhase = RacePhase(
                name: phase.name,
                phaseType: phase.phaseType,
                duration: phase.duration,
                distance: phase.distance,
                targetStrokeRate: phase.targetStrokeRate,
                strokeDistance: phase.strokeDistance,
                allowsSubstitution: phase.allowsSubstitution
            )
            newPhase.substitutionTiming = phase.substitutionTiming
            newStrategy.phases.append(newPhase)
        }
        
        return newStrategy
    }
}
