//
//  StrategyManager.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

// 策略管理器代理
protocol StrategyManagerDelegate: AnyObject {
    func strategyManager(_ manager: StrategyManager, didUpdateStrategy strategy: RaceStrategy)
    func strategyManager(_ manager: StrategyManager, didDeleteStrategy strategy: RaceStrategy)
    func strategyManager(_ manager: StrategyManager, didSetCurrentStrategy strategy: RaceStrategy?)
}

// 成绩预测结果
struct PerformancePrediction {
    let predictedTime: TimeInterval
    let confidence: Double // 0.0 - 1.0
    let recommendations: [String]
    let riskFactors: [String]
    
    var formattedTime: String {
        return TimeFormatter.formatTime(predictedTime)
    }
    
    var confidencePercentage: Int {
        return Int(confidence * 100)
    }
}

class StrategyManager: NSObject {
    static let shared = StrategyManager()
    
    weak var delegate: StrategyManagerDelegate?
    
    // 存储的策略
    private(set) var strategies: [RaceStrategy] = []
    
    // 当前选中的策略
    private(set) var currentStrategy: RaceStrategy?
    
    // 用户数据存储键
    private let strategiesKey = "SavedRaceStrategies"
    private let currentStrategyKey = "CurrentRaceStrategy"
    
    override init() {
        super.init()
        loadStrategies()
        loadCurrentStrategy()
    }
    
    // MARK: - 策略管理
    
    // 保存策略
    func saveStrategy(_ strategy: RaceStrategy) {
        strategy.updateModificationTime()
        
        if let index = strategies.firstIndex(where: { $0.id == strategy.id }) {
            strategies[index] = strategy
        } else {
            strategies.append(strategy)
        }
        
        saveStrategies()
        delegate?.strategyManager(self, didUpdateStrategy: strategy)
    }
    
    // 删除策略
    func deleteStrategy(_ strategy: RaceStrategy) {
        strategies.removeAll { $0.id == strategy.id }
        
        if currentStrategy?.id == strategy.id {
            currentStrategy = nil
            saveCurrentStrategy()
            delegate?.strategyManager(self, didSetCurrentStrategy: nil)
        }
        
        saveStrategies()
        delegate?.strategyManager(self, didDeleteStrategy: strategy)
    }
    
    // 设置当前策略
    func setCurrentStrategy(_ strategy: RaceStrategy?) {
        currentStrategy = strategy
        saveCurrentStrategy()
        delegate?.strategyManager(self, didSetCurrentStrategy: strategy)
    }
    
    // 获取自定义策略列表
    var customStrategies: [RaceStrategy] {
        return strategies.sorted { $0.lastModified > $1.lastModified }
    }
    
    // MARK: - 成绩预测
    
    // 基于历史数据预测成绩
    func predictPerformance(for team: Team, using strategy: RaceStrategy) -> PerformancePrediction {
        // 获取队伍历史成绩数据
        let raceManager = RaceManager.shared
        let teamHistory = raceManager.completedRaces.compactMap { race in
            race.teams.first { $0.id == team.id }
        }
        
        var predictedTime = strategy.targetTime
        var confidence: Double = 0.5 // 基础置信度
        var recommendations: [String] = []
        var riskFactors: [String] = []
        
        // 如果有历史数据，进行更精确的预测
        if !teamHistory.isEmpty {
            let averageTime = teamHistory.compactMap { $0.finishTime }.reduce(0, +) / Double(teamHistory.count)
            let bestTime = teamHistory.compactMap { $0.finishTime }.min() ?? strategy.targetTime
            
            // 基于历史表现调整预测
            let performanceFactor = bestTime / strategy.targetTime
            predictedTime = strategy.targetTime * performanceFactor
            
            // 提高置信度
            confidence = min(0.9, 0.5 + Double(teamHistory.count) * 0.1)
            
            // 生成建议
            if performanceFactor < 0.95 {
                recommendations.append("队伍历史表现优秀，可以考虑更激进的策略")
            } else if performanceFactor > 1.1 {
                recommendations.append("建议采用保守策略，确保完成比赛")
                riskFactors.append("历史成绩显示可能需要更多训练时间")
            }
        }
        
        // 分析策略风险
        analyzeStrategyRisks(strategy: strategy, recommendations: &recommendations, riskFactors: &riskFactors)
        
        return PerformancePrediction(
            predictedTime: predictedTime,
            confidence: confidence,
            recommendations: recommendations,
            riskFactors: riskFactors
        )
    }
    
    // 分析策略风险
    private func analyzeStrategyRisks(strategy: RaceStrategy, recommendations: inout [String], riskFactors: inout [String]) {
        let maxStrokeRate = strategy.phases.map { $0.targetStrokeRate }.max() ?? 0
        let minStrokeRate = strategy.phases.map { $0.targetStrokeRate }.min() ?? 0
        let strokeRateVariation = maxStrokeRate - minStrokeRate
        
        // 划频分析
        if maxStrokeRate > 100 {
            riskFactors.append("最高划频超过100 SPM，可能导致体力过早消耗")
            recommendations.append("考虑降低峰值划频，延长持续时间")
        }
        
        if strokeRateVariation > 20 {
            riskFactors.append("划频变化幅度较大，需要良好的节奏控制")
            recommendations.append("加强节奏变换训练，确保平稳过渡")
        }
        
        // 换人时机分析
        let substitutionCount = strategy.substitutionTimings.count
        if substitutionCount > 2 {
            riskFactors.append("换人次数较多，可能影响节奏连贯性")
        } else if substitutionCount == 0 && strategy.raceDistance > 1000 {
            recommendations.append("长距离比赛建议安排换人，保持体力")
        }
        
        // 阶段分配分析
        let startPhase = strategy.phases.first { $0.phaseType == .start }
        if let start = startPhase, start.targetStrokeRate > strategy.averageStrokeRate + 15 {
            riskFactors.append("起步划频过高，可能影响后程表现")
        }
        
        // 策略类型特定建议
        switch strategy.strategyType {
        case .startBurst:
            recommendations.append("起步爆发型策略需要优秀的起步技术和体能储备")
        case .finishSprint:
            recommendations.append("冲刺终结型策略需要精确的体力分配和强大的冲刺能力")
        case .negative:
            recommendations.append("负分段策略考验心理素质，需要抵制前程跟风的诱惑")
        case .positive:
            recommendations.append("正分段策略需要强大的前程能力和良好的体能基础")
        default:
            break
        }
    }
    
    // MARK: - 数据持久化
    
    private func saveStrategies() {
        do {
            let data = try JSONEncoder().encode(strategies)
            UserDefaults.standard.set(data, forKey: strategiesKey)
        } catch {
            print("Failed to save strategies: \(error)")
        }
    }
    
    private func loadStrategies() {
        guard let data = UserDefaults.standard.data(forKey: strategiesKey) else { return }
        
        do {
            strategies = try JSONDecoder().decode([RaceStrategy].self, from: data)
        } catch {
            print("Failed to load strategies: \(error)")
            strategies = []
        }
    }
    
    private func saveCurrentStrategy() {
        if let strategy = currentStrategy {
            do {
                let data = try JSONEncoder().encode(strategy)
                UserDefaults.standard.set(data, forKey: currentStrategyKey)
            } catch {
                print("Failed to save current strategy: \(error)")
            }
        } else {
            UserDefaults.standard.removeObject(forKey: currentStrategyKey)
        }
    }
    
    private func loadCurrentStrategy() {
        guard let data = UserDefaults.standard.data(forKey: currentStrategyKey) else { return }
        
        do {
            currentStrategy = try JSONDecoder().decode(RaceStrategy.self, from: data)
        } catch {
            print("Failed to load current strategy: \(error)")
            currentStrategy = nil
        }
    }
    
    // MARK: - 工具方法
    
    // 创建新策略
    func createNewStrategy(name: String, distance: Double, targetTime: TimeInterval, type: StrategyType = .custom) -> RaceStrategy {
        let strategy = StrategyTemplateGenerator.createStrategy(
            type: type,
            distance: distance,
            targetTime: targetTime
        )
        strategy.name = name
        strategy.strategyType = type
        return strategy
    }
    
    // 复制策略
    func duplicateStrategy(_ strategy: RaceStrategy) -> RaceStrategy {
        let newStrategy = RaceStrategy(
            name: "\(strategy.name) - 副本",
            strategyType: strategy.strategyType,
            raceDistance: strategy.raceDistance,
            targetTime: strategy.targetTime
        )
        
        // 复制所有阶段
        for phase in strategy.phases {
            let newPhase = RacePhase(
                name: phase.name,
                phaseType: phase.phaseType,
                duration: phase.duration,
                distance: phase.distance,
                targetStrokeRate: phase.targetStrokeRate,
                strokeDistance: phase.strokeDistance,
                allowsSubstitution: phase.allowsSubstitution
            )
            newPhase.substitutionTiming = phase.substitutionTiming
            newStrategy.phases.append(newPhase)
        }
        
        return newStrategy
    }
}
