//
//  StrategyManager.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Protocol for strategy manager delegate
protocol StrategyManagerDelegate: AnyObject {
    func strategyManager(_ manager: StrategyManager, didUpdateStrategy strategy: PaceStrategy)
    func strategyManager(_ manager: StrategyManager, didDeleteStrategy strategy: PaceStrategy)
}

/// Manages pace strategies and provides persistence
class StrategyManager: NSObject {
    
    // MARK: - Properties
    
    /// Shared singleton instance
    static let shared = StrategyManager()
    
    /// Delegate for strategy updates
    weak var delegate: StrategyManagerDelegate?
    
    /// All saved strategies
    private(set) var strategies: [PaceStrategy] = []
    
    /// Currently selected strategy
    private(set) var currentStrategy: PaceStrategy?
    
    /// UserDefaults key for storing strategies
    private let strategiesKey = "DragonPaceStrategies"
    
    /// Common race distances (in meters)
    static let commonDistances: [Double] = [200, 250, 500, 1000, 2000]
    
    /// Common stroke distances (in meters per stroke)
    static let commonStrokeDistances: [Double] = [2.0, 2.2, 2.5, 2.8, 3.0]
    
    // MARK: - Initialization
    
    private override init() {
        super.init()
        loadStrategies()
        createDefaultTemplatesIfNeeded()
    }
    
    // MARK: - Strategy Management
    
    /// Create a new strategy
    /// - Parameters:
    ///   - name: Strategy name
    ///   - distance: Race distance
    ///   - targetTime: Target time
    ///   - strokeDistance: Average stroke distance
    ///   - type: Strategy type
    /// - Returns: Created strategy
    func createStrategy(name: String,
                       distance: Double,
                       targetTime: TimeInterval,
                       strokeDistance: Double = 2.5,
                       type: StrategyType = .custom) -> PaceStrategy {
        
        let strategy: PaceStrategy
        
        if type == .custom {
            strategy = PaceStrategy(
                name: name,
                raceDistance: distance,
                targetTime: targetTime,
                averageStrokeDistance: strokeDistance,
                strategyType: type,
                isCustom: true
            )
        } else {
            strategy = StrategyTemplate.createStrategy(
                type: type,
                distance: distance,
                targetTime: targetTime,
                strokeDistance: strokeDistance
            )
            strategy.name = name
        }
        
        strategies.append(strategy)
        currentStrategy = strategy
        
        saveStrategies()
        delegate?.strategyManager(self, didUpdateStrategy: strategy)
        
        return strategy
    }
    
    /// Save an existing strategy
    /// - Parameter strategy: Strategy to save
    func saveStrategy(_ strategy: PaceStrategy) {
        strategy.modifiedAt = Date()
        
        if !strategies.contains(where: { $0.id == strategy.id }) {
            strategies.append(strategy)
        }
        
        saveStrategies()
        delegate?.strategyManager(self, didUpdateStrategy: strategy)
    }
    
    /// Delete a strategy
    /// - Parameter strategy: Strategy to delete
    /// - Returns: True if successful
    @discardableResult
    func deleteStrategy(_ strategy: PaceStrategy) -> Bool {
        guard let index = strategies.firstIndex(where: { $0.id == strategy.id }) else {
            return false
        }
        
        strategies.remove(at: index)
        
        if currentStrategy?.id == strategy.id {
            currentStrategy = nil
        }
        
        saveStrategies()
        delegate?.strategyManager(self, didDeleteStrategy: strategy)
        
        return true
    }
    
    /// Duplicate a strategy
    /// - Parameter strategy: Strategy to duplicate
    /// - Returns: New duplicated strategy
    func duplicateStrategy(_ strategy: PaceStrategy) -> PaceStrategy {
        let newStrategy = PaceStrategy(
            name: "\(strategy.name) Copy",
            raceDistance: strategy.raceDistance,
            targetTime: strategy.targetTime,
            averageStrokeDistance: strategy.averageStrokeDistance,
            strategyType: strategy.strategyType,
            isCustom: true
        )
        
        // Copy phases
        for phase in strategy.phases {
            let newPhase = RhythmPhase(
                name: phase.name,
                duration: phase.duration,
                strokeRate: phase.strokeRate,
                distance: phase.distance,
                strokeDistance: phase.strokeDistance,
                phaseType: phase.phaseType,
                allowsSubstitution: phase.allowsSubstitution
            )
            newPhase.substitutionTiming = phase.substitutionTiming
            newStrategy.addPhase(newPhase)
        }
        
        newStrategy.notes = strategy.notes
        
        strategies.append(newStrategy)
        saveStrategies()
        
        return newStrategy
    }
    
    /// Set current strategy
    /// - Parameter strategy: Strategy to set as current
    func setCurrentStrategy(_ strategy: PaceStrategy) {
        currentStrategy = strategy
    }
    
    // MARK: - Template Management
    
    /// Create default template strategies if none exist
    private func createDefaultTemplatesIfNeeded() {
        let hasTemplates = strategies.contains { !$0.isCustom }
        guard !hasTemplates else { return }
        
        // Create templates for common distances
        for distance in Self.commonDistances {
            let targetTime = estimateTargetTime(for: distance)
            
            for templateType in StrategyTemplate.getAvailableTemplates() {
                let template = StrategyTemplate.createStrategy(
                    type: templateType,
                    distance: distance,
                    targetTime: targetTime
                )
                template.name = "\(templateType.displayName) - \(Int(distance))m"
                strategies.append(template)
            }
        }
        
        saveStrategies()
    }
    
    /// Estimate reasonable target time for a distance
    /// - Parameter distance: Race distance in meters
    /// - Returns: Estimated time in seconds
    private func estimateTargetTime(for distance: Double) -> TimeInterval {
        // Based on typical dragon boat speeds (12-18 km/h)
        let averageSpeed = 15.0 / 3.6 // 15 km/h in m/s
        return distance / averageSpeed
    }
    
    // MARK: - Performance Analysis
    
    /// Predict performance for a team using a strategy
    /// - Parameters:
    ///   - team: Team to analyze
    ///   - strategy: Strategy to use
    /// - Returns: Performance prediction
    func predictPerformance(for team: Team, using strategy: PaceStrategy) -> PerformancePrediction {
        // Use historical data if available
        let raceManager = RaceManager.shared
        let teamRaces = raceManager.completedRaces.filter { race in
            race.teams.contains { $0.name == team.name && $0.isFinished }
        }
        
        var baselineTime = strategy.targetTime
        var confidence = 0.5 // Default confidence
        
        if !teamRaces.isEmpty {
            // Calculate average performance
            let teamTimes = teamRaces.compactMap { race in
                race.teams.first { $0.name == team.name }?.finishTime
            }
            
            if !teamTimes.isEmpty {
                let averageTime = teamTimes.reduce(0, +) / Double(teamTimes.count)
                let variance = teamTimes.map { pow($0 - averageTime, 2) }.reduce(0, +) / Double(teamTimes.count)
                
                baselineTime = averageTime
                confidence = max(0.1, min(0.9, 1.0 - (variance / averageTime)))
            }
        }
        
        return PerformancePrediction(
            team: team,
            strategy: strategy,
            predictedTime: baselineTime,
            confidence: confidence,
            basedOnRaces: teamRaces.count
        )
    }
    
    // MARK: - Persistence
    
    /// Save strategies to UserDefaults
    private func saveStrategies() {
        do {
            let data = try JSONEncoder().encode(strategies)
            UserDefaults.standard.set(data, forKey: strategiesKey)
        } catch {
            print("Failed to save strategies: \(error)")
        }
    }
    
    /// Load strategies from UserDefaults
    private func loadStrategies() {
        guard let data = UserDefaults.standard.data(forKey: strategiesKey) else {
            return
        }
        
        do {
            strategies = try JSONDecoder().decode([PaceStrategy].self, from: data)
        } catch {
            print("Failed to load strategies: \(error)")
            strategies = []
        }
    }
    
    // MARK: - Computed Properties
    
    /// Get custom strategies (user-created)
    var customStrategies: [PaceStrategy] {
        return strategies.filter { $0.isCustom }.sorted { $0.modifiedAt > $1.modifiedAt }
    }
    
    /// Get template strategies (predefined)
    var templateStrategies: [PaceStrategy] {
        return strategies.filter { !$0.isCustom }.sorted { $0.name < $1.name }
    }
    
    /// Get strategies for a specific distance
    /// - Parameter distance: Race distance
    /// - Returns: Strategies matching the distance
    func strategies(for distance: Double, tolerance: Double = 50.0) -> [PaceStrategy] {
        return strategies.filter { abs($0.raceDistance - distance) <= tolerance }
    }
}

// MARK: - Performance Prediction

struct PerformancePrediction {
    let team: Team
    let strategy: PaceStrategy
    let predictedTime: TimeInterval
    let confidence: Double // 0.0 to 1.0
    let basedOnRaces: Int
    
    var formattedPredictedTime: String {
        return TimeFormatter.formatTime(predictedTime)
    }
    
    var confidenceDescription: String {
        switch confidence {
        case 0.8...1.0:
            return "High"
        case 0.5..<0.8:
            return "Medium"
        default:
            return "Low"
        }
    }
    
    var timeDifference: TimeInterval {
        return predictedTime - strategy.targetTime
    }
    
    var isSlowerThanTarget: Bool {
        return timeDifference > 0
    }
}
