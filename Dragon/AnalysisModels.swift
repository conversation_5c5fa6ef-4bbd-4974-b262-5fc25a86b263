//
//  AnalysisModels.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

// MARK: - Team Statistics

class TeamStatistics {
    let name: String
    private var races: [TeamRaceData] = []
    
    init(name: String) {
        self.name = name
    }
    
    func addRace(team: Team) {
        let raceData = TeamRaceData(
            finishTime: team.finishTime,
            rank: team.rank,
            isFinished: team.isFinished
        )
        races.append(raceData)
    }
    
    // MARK: - Computed Properties
    
    var totalRaces: Int {
        return races.count
    }
    
    var finishedRaces: Int {
        return races.filter { $0.isFinished }.count
    }
    
    var averageTime: TimeInterval? {
        let finishedTimes = races.compactMap { $0.finishTime }
        guard !finishedTimes.isEmpty else { return nil }
        return finishedTimes.reduce(0, +) / Double(finishedTimes.count)
    }
    
    var bestTime: TimeInterval? {
        return races.compactMap { $0.finishTime }.min()
    }
    
    var worstTime: TimeInterval? {
        return races.compactMap { $0.finishTime }.max()
    }
    
    var averageRank: Double? {
        let ranks = races.compactMap { $0.rank }
        guard !ranks.isEmpty else { return nil }
        return Double(ranks.reduce(0, +)) / Double(ranks.count)
    }
    
    var bestRank: Int? {
        return races.compactMap { $0.rank }.min()
    }
    
    var winRate: Double {
        let wins = races.filter { $0.rank == 1 }.count
        guard totalRaces > 0 else { return 0 }
        return Double(wins) / Double(totalRaces)
    }
    
    var podiumRate: Double {
        let podiums = races.filter { ($0.rank ?? Int.max) <= 3 }.count
        guard totalRaces > 0 else { return 0 }
        return Double(podiums) / Double(totalRaces)
    }
    
    var completionRate: Double {
        guard totalRaces > 0 else { return 0 }
        return Double(finishedRaces) / Double(totalRaces)
    }
    
    // MARK: - Performance Trends
    
    var isImproving: Bool {
        guard races.count >= 3 else { return false }
        
        let recentRaces = Array(races.suffix(3))
        let recentTimes = recentRaces.compactMap { $0.finishTime }
        
        guard recentTimes.count >= 2 else { return false }
        
        // Check if recent times are generally decreasing (improving)
        let firstHalf = Array(recentTimes.prefix(recentTimes.count / 2))
        let secondHalf = Array(recentTimes.suffix(recentTimes.count - recentTimes.count / 2))
        
        let firstAvg = firstHalf.reduce(0, +) / Double(firstHalf.count)
        let secondAvg = secondHalf.reduce(0, +) / Double(secondHalf.count)
        
        return secondAvg < firstAvg
    }
    
    var performanceTrend: PerformanceTrend {
        guard races.count >= 2 else { return .stable }
        
        let recentRaces = Array(races.suffix(min(5, races.count)))
        let times = recentRaces.compactMap { $0.finishTime }
        
        guard times.count >= 2 else { return .stable }
        
        let firstTime = times.first!
        let lastTime = times.last!
        let improvement = (firstTime - lastTime) / firstTime
        
        if improvement > 0.05 { // 5% improvement
            return .improving
        } else if improvement < -0.05 { // 5% decline
            return .declining
        } else {
            return .stable
        }
    }
}

// MARK: - Supporting Types

struct TeamRaceData {
    let finishTime: TimeInterval?
    let rank: Int?
    let isFinished: Bool
}

enum PerformanceTrend {
    case improving
    case declining
    case stable
    
    var displayName: String {
        switch self {
        case .improving: return "Improving"
        case .declining: return "Declining"
        case .stable: return "Stable"
        }
    }
    
    var color: UIColor {
        switch self {
        case .improving: return UIColor.systemGreen
        case .declining: return UIColor.systemRed
        case .stable: return UIColor.systemBlue
        }
    }
}

// MARK: - Date Range

enum DateRange {
    case lastDays(Int)
    case lastMonths(Int)
    case custom(start: Date, end: Date)
    
    func contains(_ date: Date) -> Bool {
        let now = Date()
        let calendar = Calendar.current
        
        switch self {
        case .lastDays(let days):
            guard let startDate = calendar.date(byAdding: .day, value: -days, to: now) else { return false }
            return date >= startDate && date <= now
            
        case .lastMonths(let months):
            guard let startDate = calendar.date(byAdding: .month, value: -months, to: now) else { return false }
            return date >= startDate && date <= now
            
        case .custom(let start, let end):
            return date >= start && date <= end
        }
    }
}

// MARK: - Chart Data

struct ChartDataPoint {
    let x: Double
    let y: Double
    let label: String?
    let date: Date?
    
    init(x: Double, y: Double, label: String? = nil, date: Date? = nil) {
        self.x = x
        self.y = y
        self.label = label
        self.date = date
    }
}

struct ChartSeries {
    let name: String
    let color: UIColor
    let points: [ChartDataPoint]
    
    init(name: String, color: UIColor, points: [ChartDataPoint]) {
        self.name = name
        self.color = color
        self.points = points
    }
}

// MARK: - Race Comparison Data

struct RaceComparisonData {
    let raceName: String
    let raceDate: Date
    let teams: [TeamComparisonData]
    
    var fastestTime: TimeInterval? {
        return teams.compactMap { $0.finishTime }.min()
    }
    
    var averageTime: TimeInterval? {
        let times = teams.compactMap { $0.finishTime }
        guard !times.isEmpty else { return nil }
        return times.reduce(0, +) / Double(times.count)
    }
}

struct TeamComparisonData {
    let teamName: String
    let finishTime: TimeInterval?
    let rank: Int?
    let isFinished: Bool
    
    var timeDifferenceFromWinner: TimeInterval? {
        // This would be calculated when creating the comparison data
        return nil
    }
}

// MARK: - Analysis Summary

struct AnalysisSummary {
    let totalRaces: Int
    let totalTeams: Int
    let averageRaceTime: TimeInterval?
    let fastestOverallTime: TimeInterval?
    let mostActiveTeam: String?
    let mostSuccessfulTeam: String?
    let dateRange: DateRange?
    
    var formattedAverageTime: String {
        guard let time = averageRaceTime else { return "N/A" }
        return TimeFormatter.formatTime(time)
    }
    
    var formattedFastestTime: String {
        guard let time = fastestOverallTime else { return "N/A" }
        return TimeFormatter.formatTime(time)
    }
}

// MARK: - Performance Metrics

struct PerformanceMetrics {
    let consistency: Double // 0.0 to 1.0, higher is more consistent
    let improvement: Double // Percentage improvement over time
    let competitiveness: Double // How often team finishes in top 50%
    let reliability: Double // Completion rate
    
    var overallScore: Double {
        return (consistency + competitiveness + reliability) / 3.0
    }
    
    var grade: String {
        switch overallScore {
        case 0.9...: return "A+"
        case 0.8..<0.9: return "A"
        case 0.7..<0.8: return "B+"
        case 0.6..<0.7: return "B"
        case 0.5..<0.6: return "C+"
        case 0.4..<0.5: return "C"
        default: return "D"
        }
    }
}
