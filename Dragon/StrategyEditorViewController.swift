//
//  StrategyEditorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategyEditorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let nameTextField = UITextField()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    
    private let phasesLabel = UILabel()
    private let phasesStackView = UIStackView()
    
    // MARK: - Properties
    
    private let strategy: RaceStrategy
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Initialization
    
    init(strategy: RaceStrategy) {
        self.strategy = strategy
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        populateData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "编辑策略"
        
        // 导航栏按钮
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "取消",
            style: .plain,
            target: self,
            action: #selector(cancelPressed)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "保存",
            style: .done,
            target: self,
            action: #selector(savePressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "策略详情"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        // Setup text fields
        nameTextField.placeholder = "策略名称"
        setupTextField(nameTextField)
        contentView.addSubview(nameTextField)
        
        distanceTextField.placeholder = "比赛距离 (米)"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        contentView.addSubview(distanceTextField)
        
        targetTimeTextField.placeholder = "目标时间 (MM:SS)"
        setupTextField(targetTimeTextField)
        contentView.addSubview(targetTimeTextField)
        
        // Setup phases
        phasesLabel.text = "比赛阶段"
        phasesLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        phasesLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(phasesLabel)
        
        phasesStackView.axis = .vertical
        phasesStackView.spacing = 12
        phasesStackView.distribution = .fill
        contentView.addSubview(phasesStackView)
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonCardBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        nameTextField.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(nameTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        phasesLabel.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(30)
            make.leading.equalToSuperview().offset(20)
        }
        
        phasesStackView.snp.makeConstraints { make in
            make.top.equalTo(phasesLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func populateData() {
        nameTextField.text = strategy.name
        distanceTextField.text = String(Int(strategy.raceDistance))
        targetTimeTextField.text = TimeFormatter.formatTime(strategy.targetTime)
        
        // 添加阶段视图
        for phase in strategy.phases {
            let phaseView = createPhaseView(for: phase)
            phasesStackView.addArrangedSubview(phaseView)
        }
    }
    
    private func createPhaseView(for phase: RacePhase) -> UIView {
        let container = UIView()
        container.backgroundColor = UIColor.dragonCardBackground
        container.layer.cornerRadius = 8
        container.layer.borderWidth = 1
        container.layer.borderColor = phase.phaseType.color.cgColor
        
        // 阶段标题
        let titleLabel = UILabel()
        titleLabel.text = phase.name
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = phase.phaseType.color
        container.addSubview(titleLabel)
        
        // 阶段信息
        let infoLabel = UILabel()
        let duration = TimeFormatter.formatTime(phase.duration)
        let distance = String(format: "%.0f", phase.distance)
        let strokeRate = String(format: "%.0f", phase.targetStrokeRate)
        let strokes = String(phase.totalStrokes)
        
        infoLabel.text = "时长: \(duration) | 距离: \(distance)m | 划频: \(strokeRate) SPM | 桨数: \(strokes)"
        infoLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        infoLabel.textColor = UIColor.dragonTextSecondary
        infoLabel.numberOfLines = 0
        container.addSubview(infoLabel)
        
        // 换人信息
        if phase.allowsSubstitution {
            let substitutionLabel = UILabel()
            if let timing = phase.substitutionTiming {
                substitutionLabel.text = "换人时机: \(TimeFormatter.formatTime(timing))"
            } else {
                substitutionLabel.text = "允许换人"
            }
            substitutionLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            substitutionLabel.textColor = UIColor.dragonAccent
            container.addSubview(substitutionLabel)
            
            substitutionLabel.snp.makeConstraints { make in
                make.top.equalTo(infoLabel.snp.bottom).offset(4)
                make.leading.trailing.equalToSuperview().inset(12)
                make.bottom.equalToSuperview().offset(-12)
            }
        } else {
            infoLabel.snp.makeConstraints { make in
                make.bottom.equalToSuperview().offset(-12)
            }
        }
        
        // 约束
        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
        }
        
        infoLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(12)
        }
        
        container.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(80)
        }
        
        return container
    }
    
    // MARK: - Actions
    
    @objc private func cancelPressed() {
        dismiss(animated: true)
    }
    
    @objc private func savePressed() {
        guard let name = nameTextField.text, !name.isEmpty else {
            showAlert(title: "错误", message: "请输入策略名称")
            return
        }
        
        guard let distanceText = distanceTextField.text,
              let distance = Double(distanceText),
              distance > 0 else {
            showAlert(title: "错误", message: "请输入有效的比赛距离")
            return
        }
        
        let targetTime = parseTime(targetTimeTextField.text ?? "2:00")
        
        // 更新策略
        strategy.name = name
        strategy.raceDistance = distance
        strategy.targetTime = targetTime
        strategy.updateModificationTime()
        
        // 保存策略
        strategyManager.saveStrategy(strategy)
        
        showAlert(title: "保存成功", message: "策略已更新") {
            self.dismiss(animated: true)
        }
    }
    
    // MARK: - Helper Methods
    
    private func parseTime(_ timeString: String) -> TimeInterval {
        let components = timeString.components(separatedBy: ":")
        if components.count == 2,
           let minutes = Double(components[0]),
           let seconds = Double(components[1]) {
            return (minutes * 60) + seconds
        }
        return 120.0 // Default 2 minutes
    }
    
    private func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
}
