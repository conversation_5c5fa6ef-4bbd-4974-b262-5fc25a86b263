//
//  StrategyEditorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategyEditorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let nameTextField = UITextField()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    
    private let phasesLabel = UILabel()
    private let phasesStackView = UIStackView()
    
    // MARK: - Properties
    
    private let strategy: RaceStrategy
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Initialization
    
    init(strategy: RaceStrategy) {
        self.strategy = strategy
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        populateData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Edit Strategy"

        // Navigation bar buttons - only set right button, let system handle back button
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "Save",
            style: .done,
            target: self,
            action: #selector(savePressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Strategy Details"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)

        // Setup text fields
        nameTextField.placeholder = "Strategy Name"
        setupTextField(nameTextField)
        contentView.addSubview(nameTextField)

        distanceTextField.placeholder = "Race Distance (meters)"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        contentView.addSubview(distanceTextField)

        targetTimeTextField.placeholder = "Target Time (MM:SS)"
        setupTextField(targetTimeTextField)
        contentView.addSubview(targetTimeTextField)

        // Setup phases
        phasesLabel.text = "Race Phases"
        phasesLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        phasesLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(phasesLabel)
        
        phasesStackView.axis = .vertical
        phasesStackView.spacing = 12
        phasesStackView.distribution = .fill
        contentView.addSubview(phasesStackView)
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonCardBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        nameTextField.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(nameTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(48)
        }
        
        phasesLabel.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(30)
            make.leading.equalToSuperview().offset(20)
        }
        
        phasesStackView.snp.makeConstraints { make in
            make.top.equalTo(phasesLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func populateData() {
        nameTextField.text = strategy.name
        distanceTextField.text = String(Int(strategy.raceDistance))
        targetTimeTextField.text = TimeFormatter.formatTime(strategy.targetTime)
        
        // Add phase views
        for phase in strategy.phases {
            let phaseView = createPhaseView(for: phase)
            phasesStackView.addArrangedSubview(phaseView)
        }
    }
    
    private func createPhaseView(for phase: RacePhase) -> UIView {
        let container = UIView()
        container.backgroundColor = UIColor.dragonCardBackground
        container.layer.cornerRadius = 8
        container.layer.borderWidth = 1
        container.layer.borderColor = phase.phaseType.color.cgColor
        
        // Phase title
        let titleLabel = UILabel()
        titleLabel.text = phase.name
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = phase.phaseType.color
        container.addSubview(titleLabel)

        // Phase info
        let infoLabel = UILabel()
        let duration = TimeFormatter.formatTime(phase.duration)
        let distance = String(format: "%.0f", phase.distance)
        let strokeRate = String(format: "%.0f", phase.targetStrokeRate)
        let strokes = String(phase.totalStrokes)

        infoLabel.text = "Duration: \(duration) | Distance: \(distance)m | Rate: \(strokeRate) SPM | Strokes: \(strokes)"
        infoLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        infoLabel.textColor = UIColor.dragonTextSecondary
        infoLabel.numberOfLines = 0
        container.addSubview(infoLabel)
        
        // Substitution info
        if phase.allowsSubstitution {
            let substitutionLabel = UILabel()
            if let timing = phase.substitutionTiming {
                substitutionLabel.text = "Substitution: \(TimeFormatter.formatTime(timing))"
            } else {
                substitutionLabel.text = "Substitution Allowed"
            }
            substitutionLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            substitutionLabel.textColor = UIColor.dragonAccent
            container.addSubview(substitutionLabel)
            
            substitutionLabel.snp.makeConstraints { make in
                make.top.equalTo(infoLabel.snp.bottom).offset(4)
                make.leading.trailing.equalToSuperview().inset(12)
                make.bottom.equalToSuperview().offset(-12)
            }
        } else {
            infoLabel.snp.makeConstraints { make in
                make.bottom.equalToSuperview().offset(-12)
            }
        }
        
        // Constraints
        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
        }
        
        infoLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(12)
        }
        
        container.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(80)
        }
        
        return container
    }
    
    // MARK: - Actions

    @objc private func savePressed() {
        guard let name = nameTextField.text, !name.isEmpty else {
            showAlert(title: "Error", message: "Please enter strategy name")
            return
        }

        guard let distanceText = distanceTextField.text,
              let distance = Double(distanceText),
              distance > 0 else {
            showAlert(title: "Error", message: "Please enter valid race distance")
            return
        }

        let targetTime = parseTime(targetTimeTextField.text ?? "2:00")

        // Update strategy
        strategy.name = name
        strategy.raceDistance = distance
        strategy.targetTime = targetTime
        strategy.updateModificationTime()

        // Save strategy
        strategyManager.saveStrategy(strategy)

        showAlert(title: "Save Successful", message: "Strategy has been updated") {
            self.dismiss(animated: true)
        }
    }
    
    // MARK: - Helper Methods
    
    private func parseTime(_ timeString: String) -> TimeInterval {
        let components = timeString.components(separatedBy: ":")
        if components.count == 2,
           let minutes = Double(components[0]),
           let seconds = Double(components[1]) {
            return (minutes * 60) + seconds
        }
        return 120.0 // Default 2 minutes
    }
    
    private func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
}
