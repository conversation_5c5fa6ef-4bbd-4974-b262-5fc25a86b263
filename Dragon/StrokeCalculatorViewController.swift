//
//  StrokeCalculatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrokeCalculatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // 输入卡片
    private let inputCard = UIView()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    private let strokeDistanceTextField = UITextField()
    private let calculateButton = UIButton(type: .system)
    
    // 结果卡片
    private let resultsCard = UIView()
    private let totalStrokesLabel = UILabel()
    private let averageStrokeRateLabel = UILabel()
    private let averageSpeedLabel = UILabel()
    private let strokesPerPhaseLabel = UILabel()
    
    // 详细分析卡片
    private let analysisCard = UIView()
    private let analysisStackView = UIStackView()
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Stroke Calculator"

        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)

        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)

        // Setup title
        titleLabel.text = "Precise Stroke Data Calculation"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)

        subtitleLabel.text = "Input stroke distance to auto-calculate total strokes and average stroke rate"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupInputCard()
        setupResultsCard()
        setupAnalysisCard()
    }
    
    private func setupInputCard() {
        inputCard.backgroundColor = UIColor.dragonCardBackground
        inputCard.layer.cornerRadius = 12
        inputCard.layer.borderWidth = 1
        inputCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(inputCard)
        
        let inputLabel = UILabel()
        inputLabel.text = "Calculation Parameters"
        inputLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        inputLabel.textColor = UIColor.dragonTextPrimary
        inputCard.addSubview(inputLabel)

        // Distance input
        distanceTextField.placeholder = "Race Distance (meters)"
        distanceTextField.text = "500"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        inputCard.addSubview(distanceTextField)

        // Target time input
        targetTimeTextField.placeholder = "Target Time (MM:SS)"
        targetTimeTextField.text = "2:00"
        setupTextField(targetTimeTextField)
        inputCard.addSubview(targetTimeTextField)

        // Stroke distance input
        strokeDistanceTextField.placeholder = "Stroke Distance (meters)"
        strokeDistanceTextField.text = "2.5"
        strokeDistanceTextField.keyboardType = .decimalPad
        setupTextField(strokeDistanceTextField)
        inputCard.addSubview(strokeDistanceTextField)

        // Calculate button
        calculateButton.setTitle("Calculate Stroke Data", for: .normal)
        calculateButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        calculateButton.setTitleColor(.white, for: .normal)
        calculateButton.backgroundColor = UIColor.dragonAccent
        calculateButton.layer.cornerRadius = 12
        calculateButton.addTarget(self, action: #selector(calculatePressed), for: .touchUpInside)
        inputCard.addSubview(calculateButton)
        
        // 约束
        inputLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(inputLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }
        
        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }
        
        strokeDistanceTextField.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }
        
        calculateButton.snp.makeConstraints { make in
            make.top.equalTo(strokeDistanceTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupResultsCard() {
        resultsCard.backgroundColor = UIColor.dragonCardBackground
        resultsCard.layer.cornerRadius = 12
        resultsCard.layer.borderWidth = 1
        resultsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(resultsCard)
        
        let resultsLabel = UILabel()
        resultsLabel.text = "Calculation Results"
        resultsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        resultsLabel.textColor = UIColor.dragonTextPrimary
        resultsCard.addSubview(resultsLabel)

        totalStrokesLabel.text = "Total Strokes: --"
        totalStrokesLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        totalStrokesLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(totalStrokesLabel)

        averageStrokeRateLabel.text = "Average Stroke Rate: -- SPM"
        averageStrokeRateLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        averageStrokeRateLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(averageStrokeRateLabel)

        averageSpeedLabel.text = "Average Speed: -- km/h"
        averageSpeedLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        averageSpeedLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(averageSpeedLabel)

        strokesPerPhaseLabel.text = "Strokes Per Phase: --"
        strokesPerPhaseLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        strokesPerPhaseLabel.textColor = UIColor.dragonTextSecondary
        strokesPerPhaseLabel.numberOfLines = 0
        resultsCard.addSubview(strokesPerPhaseLabel)
        
        // 约束
        resultsLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        totalStrokesLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        averageStrokeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(totalStrokesLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        averageSpeedLabel.snp.makeConstraints { make in
            make.top.equalTo(averageStrokeRateLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        strokesPerPhaseLabel.snp.makeConstraints { make in
            make.top.equalTo(averageSpeedLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupAnalysisCard() {
        analysisCard.backgroundColor = UIColor.dragonCardBackground
        analysisCard.layer.cornerRadius = 12
        analysisCard.layer.borderWidth = 1
        analysisCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(analysisCard)
        
        let analysisLabel = UILabel()
        analysisLabel.text = "Detailed Analysis"
        analysisLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        analysisLabel.textColor = UIColor.dragonTextPrimary
        analysisCard.addSubview(analysisLabel)
        
        analysisStackView.axis = .vertical
        analysisStackView.spacing = 8
        analysisStackView.distribution = .fill
        analysisCard.addSubview(analysisStackView)
        
        // 约束
        analysisLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        analysisStackView.snp.makeConstraints { make in
            make.top.equalTo(analysisLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonSecondaryBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        inputCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(inputCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        analysisCard.snp.makeConstraints { make in
            make.top.equalTo(resultsCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Actions

    @objc private func calculatePressed() {
        guard let distanceText = distanceTextField.text,
              let distance = Double(distanceText),
              distance > 0 else {
            showAlert(title: "Invalid Distance", message: "Please enter a valid race distance")
            return
        }

        guard let strokeDistanceText = strokeDistanceTextField.text,
              let strokeDistance = Double(strokeDistanceText),
              strokeDistance > 0 else {
            showAlert(title: "Invalid Stroke Distance", message: "Please enter a valid stroke distance")
            return
        }

        let targetTime = parseTime(targetTimeTextField.text ?? "2:00")

        // 计算基础数据
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60 // SPM
        let averageSpeed = distance / targetTime // m/s
        let averageSpeedKmh = averageSpeed * 3.6 // km/h

        // Update basic results
        totalStrokesLabel.text = "Total Strokes: \(totalStrokes) strokes"
        averageStrokeRateLabel.text = "Average Stroke Rate: \(Int(averageStrokeRate)) SPM"
        averageSpeedLabel.text = "Average Speed: \(String(format: "%.1f", averageSpeedKmh)) km/h"

        // Calculate phase distribution (standard three phases)
        let startStrokes = Int(Double(totalStrokes) * 0.2)
        let middleStrokes = Int(Double(totalStrokes) * 0.6)
        let sprintStrokes = totalStrokes - startStrokes - middleStrokes

        strokesPerPhaseLabel.text = "Strokes Per Phase:\nStart: \(startStrokes) strokes\nMiddle: \(middleStrokes) strokes\nSprint: \(sprintStrokes) strokes"

        // 更新详细分析
        updateDetailedAnalysis(
            distance: distance,
            targetTime: targetTime,
            strokeDistance: strokeDistance,
            totalStrokes: totalStrokes,
            averageStrokeRate: averageStrokeRate,
            averageSpeedKmh: averageSpeedKmh
        )

        // 更新结果颜色
        [totalStrokesLabel, averageStrokeRateLabel, averageSpeedLabel, strokesPerPhaseLabel].forEach {
            $0.textColor = UIColor.dragonAccentSecondary
        }
    }

    private func updateDetailedAnalysis(distance: Double, targetTime: TimeInterval, strokeDistance: Double, totalStrokes: Int, averageStrokeRate: Double, averageSpeedKmh: Double) {
        // 清除之前的分析
        analysisStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 划频分析
        let strokeRateAnalysis = createAnalysisItem(
            title: "划频分析",
            content: getStrokeRateAnalysis(averageStrokeRate),
            color: getStrokeRateColor(averageStrokeRate)
        )
        analysisStackView.addArrangedSubview(strokeRateAnalysis)

        // 速度分析
        let speedAnalysis = createAnalysisItem(
            title: "速度分析",
            content: getSpeedAnalysis(averageSpeedKmh, distance: distance),
            color: getSpeedColor(averageSpeedKmh, distance: distance)
        )
        analysisStackView.addArrangedSubview(speedAnalysis)

        // 桨距分析
        let strokeDistanceAnalysis = createAnalysisItem(
            title: "桨距分析",
            content: getStrokeDistanceAnalysis(strokeDistance),
            color: getStrokeDistanceColor(strokeDistance)
        )
        analysisStackView.addArrangedSubview(strokeDistanceAnalysis)

        // 比赛建议
        let recommendations = createAnalysisItem(
            title: "比赛建议",
            content: getRecommendations(distance: distance, targetTime: targetTime, strokeRate: averageStrokeRate),
            color: UIColor.dragonAccent
        )
        analysisStackView.addArrangedSubview(recommendations)
    }

    private func createAnalysisItem(title: String, content: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = UIColor.dragonSecondaryBackground
        container.layer.cornerRadius = 8
        container.layer.borderWidth = 1
        container.layer.borderColor = color.cgColor

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = color
        container.addSubview(titleLabel)

        let contentLabel = UILabel()
        contentLabel.text = content
        contentLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        contentLabel.textColor = UIColor.dragonTextSecondary
        contentLabel.numberOfLines = 0
        container.addSubview(contentLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(12)
            make.trailing.equalToSuperview().offset(-12)
        }

        contentLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(12)
            make.bottom.equalToSuperview().offset(-12)
        }

        return container
    }

    // MARK: - Analysis Methods

    private func getStrokeRateAnalysis(_ strokeRate: Double) -> String {
        switch strokeRate {
        case 0..<50:
            return "划频偏低，可能无法达到目标时间。建议增加桨距或提高划频。"
        case 50..<70:
            return "划频适中，适合长距离比赛。注意保持节奏稳定。"
        case 70..<90:
            return "划频较高，适合中短距离比赛。需要良好的体能支撑。"
        case 90..<110:
            return "划频很高，适合短距离冲刺。注意体力分配，避免过早疲劳。"
        default:
            return "划频过高，可能难以维持。建议调整策略或增加桨距。"
        }
    }

    private func getStrokeRateColor(_ strokeRate: Double) -> UIColor {
        switch strokeRate {
        case 0..<50: return UIColor.systemRed
        case 50..<70: return UIColor.systemGreen
        case 70..<90: return UIColor.systemBlue
        case 90..<110: return UIColor.systemOrange
        default: return UIColor.systemRed
        }
    }

    private func getSpeedAnalysis(_ speed: Double, distance: Double) -> String {
        let expectedSpeed: Double
        switch distance {
        case 0..<300: expectedSpeed = 18.0  // 短距离
        case 300..<800: expectedSpeed = 16.0 // 中距离
        case 800..<1500: expectedSpeed = 14.0 // 长距离
        default: expectedSpeed = 12.0 // 超长距离
        }

        if speed >= expectedSpeed * 1.1 {
            return "速度优秀，超过预期\(String(format: "%.1f", (speed/expectedSpeed - 1) * 100))%。"
        } else if speed >= expectedSpeed * 0.9 {
            return "速度良好，符合预期水平。"
        } else {
            return "速度偏低，建议优化技术或调整策略。"
        }
    }

    private func getSpeedColor(_ speed: Double, distance: Double) -> UIColor {
        let expectedSpeed: Double
        switch distance {
        case 0..<300: expectedSpeed = 18.0
        case 300..<800: expectedSpeed = 16.0
        case 800..<1500: expectedSpeed = 14.0
        default: expectedSpeed = 12.0
        }

        if speed >= expectedSpeed * 1.1 {
            return UIColor.systemGreen
        } else if speed >= expectedSpeed * 0.9 {
            return UIColor.systemBlue
        } else {
            return UIColor.systemOrange
        }
    }

    private func getStrokeDistanceAnalysis(_ strokeDistance: Double) -> String {
        switch strokeDistance {
        case 0..<2.0:
            return "桨距较短，适合高频率划桨，但需要更多桨数。"
        case 2.0..<2.5:
            return "桨距适中，平衡了效率和频率。"
        case 2.5..<3.0:
            return "桨距较长，每桨效率高，但对技术要求更高。"
        default:
            return "桨距很长，需要优秀的技术和力量支撑。"
        }
    }

    private func getStrokeDistanceColor(_ strokeDistance: Double) -> UIColor {
        switch strokeDistance {
        case 0..<2.0: return UIColor.systemOrange
        case 2.0..<3.0: return UIColor.systemGreen
        default: return UIColor.systemBlue
        }
    }

    private func getRecommendations(distance: Double, targetTime: TimeInterval, strokeRate: Double) -> String {
        var recommendations: [String] = []

        // 基于距离的建议
        if distance <= 500 {
            recommendations.append("短距离比赛，建议采用高强度策略")
        } else if distance <= 1000 {
            recommendations.append("中距离比赛，注意体力分配")
        } else {
            recommendations.append("长距离比赛，重点关注耐力和节奏")
        }

        // 基于划频的建议
        if strokeRate > 80 {
            recommendations.append("高划频策略，确保充分热身")
        } else if strokeRate < 60 {
            recommendations.append("低划频策略，注意保持桨距")
        }

        // 基于时间的建议
        if targetTime < 120 {
            recommendations.append("目标时间较短，需要爆发力")
        } else if targetTime > 300 {
            recommendations.append("目标时间较长，重点关注耐力")
        }

        return recommendations.joined(separator: "\n")
    }

    // MARK: - Helper Methods

    private func parseTime(_ timeString: String) -> TimeInterval {
        let components = timeString.components(separatedBy: ":")
        if components.count == 2,
           let minutes = Double(components[0]),
           let seconds = Double(components[1]) {
            return (minutes * 60) + seconds
        }
        return 120.0 // Default 2 minutes
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}
