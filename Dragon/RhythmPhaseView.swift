//
//  RhythmPhaseView.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

protocol RhythmPhaseViewDelegate: AnyObject {
    func rhythmPhaseViewDidChange(_ phaseView: RhythmPhaseView)
    func rhythmPhaseViewDidRequestRemoval(_ phaseView: RhythmPhaseView)
}

class RhythmPhaseView: UIView {
    
    // MARK: - UI Components
    
    private let containerView = UIView()
    private let headerView = UIView()
    private let phaseIconImageView = UIImageView()
    private let phaseNameTextField = UITextField()
    private let removeButton = UIButton(type: .system)
    
    private let parametersStackView = UIStackView()
    private let durationTextField = UITextField()
    private let strokeRateTextField = UITextField()
    private let distanceTextField = UITextField()
    
    private let resultsLabel = UILabel()
    private let substitutionSwitch = UISwitch()
    private let substitutionLabel = UILabel()
    
    // MARK: - Properties
    
    let phase: RhythmPhase
    weak var delegate: RhythmPhaseViewDelegate?
    
    // MARK: - Initialization
    
    init(phase: RhythmPhase) {
        self.phase = phase
        super.init(frame: .zero)
        setupUI()
        setupConstraints()
        loadPhaseData()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // Container
        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = phase.phaseType.color.cgColor
        addSubview(containerView)
        
        // Header
        headerView.backgroundColor = phase.phaseType.color.withAlphaComponent(0.1)
        containerView.addSubview(headerView)
        
        // Phase icon
        phaseIconImageView.image = UIImage(systemName: phase.phaseType.icon)
        phaseIconImageView.tintColor = phase.phaseType.color
        phaseIconImageView.contentMode = .scaleAspectFit
        headerView.addSubview(phaseIconImageView)
        
        // Phase name
        phaseNameTextField.text = phase.name
        phaseNameTextField.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        phaseNameTextField.textColor = UIColor.dragonTextPrimary
        phaseNameTextField.backgroundColor = .clear
        phaseNameTextField.borderStyle = .none
        phaseNameTextField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        headerView.addSubview(phaseNameTextField)
        
        // Remove button
        removeButton.setImage(UIImage(systemName: "minus.circle.fill"), for: .normal)
        removeButton.tintColor = UIColor.dragonError
        removeButton.addTarget(self, action: #selector(removePressed), for: .touchUpInside)
        headerView.addSubview(removeButton)
        
        // Parameters stack
        parametersStackView.axis = .horizontal
        parametersStackView.spacing = 12
        parametersStackView.distribution = .fillEqually
        containerView.addSubview(parametersStackView)
        
        // Duration field
        durationTextField.placeholder = "Duration (s)"
        setupParameterTextField(durationTextField)
        parametersStackView.addArrangedSubview(durationTextField)
        
        // Stroke rate field
        strokeRateTextField.placeholder = "Rate (SPM)"
        setupParameterTextField(strokeRateTextField)
        parametersStackView.addArrangedSubview(strokeRateTextField)
        
        // Distance field
        distanceTextField.placeholder = "Distance (m)"
        setupParameterTextField(distanceTextField)
        parametersStackView.addArrangedSubview(distanceTextField)
        
        // Results
        resultsLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        resultsLabel.textColor = UIColor.dragonTextSecondary
        resultsLabel.numberOfLines = 0
        containerView.addSubview(resultsLabel)
        
        // Substitution controls
        substitutionLabel.text = "Allow Substitution"
        substitutionLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        substitutionLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(substitutionLabel)
        
        substitutionSwitch.isOn = phase.allowsSubstitution
        substitutionSwitch.onTintColor = UIColor.dragonAccent
        substitutionSwitch.addTarget(self, action: #selector(substitutionChanged), for: .valueChanged)
        containerView.addSubview(substitutionSwitch)
    }
    
    private func setupParameterTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonSecondaryBackground
        textField.layer.cornerRadius = 6
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.textAlignment = .center
        textField.keyboardType = .decimalPad
        textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        
        phaseIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        phaseNameTextField.snp.makeConstraints { make in
            make.leading.equalTo(phaseIconImageView.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.trailing.lessThanOrEqualTo(removeButton.snp.leading).offset(-8)
        }
        
        removeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        parametersStackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(12)
            make.height.equalTo(36)
        }
        
        resultsLabel.snp.makeConstraints { make in
            make.top.equalTo(parametersStackView.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(12)
        }
        
        substitutionLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(12)
            make.leading.equalToSuperview().offset(12)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        substitutionSwitch.snp.makeConstraints { make in
            make.centerY.equalTo(substitutionLabel)
            make.trailing.equalToSuperview().offset(-12)
        }
    }
    
    // MARK: - Data Loading
    
    private func loadPhaseData() {
        phaseNameTextField.text = phase.name
        durationTextField.text = String(Int(phase.duration))
        strokeRateTextField.text = String(Int(phase.strokeRate))
        distanceTextField.text = String(Int(phase.distance))
        substitutionSwitch.isOn = phase.allowsSubstitution
        
        updateResults()
    }
    
    // MARK: - Actions
    
    @objc private func textFieldDidChange() {
        updatePhaseFromUI()
        updateResults()
        delegate?.rhythmPhaseViewDidChange(self)
    }
    
    @objc private func substitutionChanged() {
        phase.allowsSubstitution = substitutionSwitch.isOn
        if substitutionSwitch.isOn && phase.substitutionTiming == nil {
            phase.substitutionTiming = 0.5 // Default to 50%
        }
        delegate?.rhythmPhaseViewDidChange(self)
    }
    
    @objc private func removePressed() {
        delegate?.rhythmPhaseViewDidRequestRemoval(self)
    }
    
    // MARK: - Helper Methods
    
    private func updatePhaseFromUI() {
        phase.name = phaseNameTextField.text ?? "Phase"
        phase.duration = Double(durationTextField.text ?? "0") ?? 0
        phase.strokeRate = Double(strokeRateTextField.text ?? "0") ?? 0
        phase.distance = Double(distanceTextField.text ?? "0") ?? 0
    }
    
    private func updateResults() {
        let strokes = phase.totalStrokes
        let speed = phase.averageSpeed * 3.6 // Convert to km/h
        
        resultsLabel.text = "Strokes: \(strokes) • Speed: \(String(format: "%.1f", speed)) km/h"
        
        // Update border color based on phase type
        containerView.layer.borderColor = phase.phaseType.color.cgColor
        headerView.backgroundColor = phase.phaseType.color.withAlphaComponent(0.1)
        phaseIconImageView.tintColor = phase.phaseType.color
    }
    
    // MARK: - Public Methods
    
    func updatePhaseType(_ phaseType: PhaseType) {
        phase.phaseType = phaseType
        phaseIconImageView.image = UIImage(systemName: phaseType.icon)
        updateResults()
    }
}
