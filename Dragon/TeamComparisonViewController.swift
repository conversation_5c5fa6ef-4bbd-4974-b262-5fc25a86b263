//
//  TeamComparisonViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit
import AAInfographics

class TeamComparisonViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // Team selection
    private let selectionCard = UIView()
    private let selectionLabel = UILabel()
    private let team1Button = UIButton(type: .system)
    private let team2Button = UIButton(type: .system)
    private let compareButton = UIButton(type: .system)
    
    // Comparison results
    private let comparisonCard = UIView()
    private let comparisonLabel = UILabel()
    private let comparisonStackView = UIStackView()
    
    // Chart card
    private let chartCard = UIView()
    private let chartLabel = UILabel()
    private let barChartView = AAChartView()
    
    // MARK: - Properties
    
    private let races: [RaceRecord]
    private var availableTeams: [String] = []
    private var selectedTeam1: String?
    private var selectedTeam2: String?
    private var teamStatistics: [String: TeamStatistics] = [:]
    
    // MARK: - Initialization
    
    init(races: [RaceRecord]) {
        self.races = races
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Team Comparison"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Compare Team Performance"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Select two teams to compare their performance statistics"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupSelectionCard()
        setupComparisonCard()
        setupChartCard()
    }
    
    private func setupSelectionCard() {
        selectionCard.backgroundColor = UIColor.dragonCardBackground
        selectionCard.layer.cornerRadius = 12
        selectionCard.layer.borderWidth = 1
        selectionCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(selectionCard)
        
        selectionLabel.text = "Select Teams to Compare"
        selectionLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        selectionLabel.textColor = UIColor.dragonTextPrimary
        selectionCard.addSubview(selectionLabel)
        
        // Team 1 button
        team1Button.setTitle("Select Team 1", for: .normal)
        setupTeamButton(team1Button)
        team1Button.addTarget(self, action: #selector(selectTeam1Pressed), for: .touchUpInside)
        selectionCard.addSubview(team1Button)
        
        // Team 2 button
        team2Button.setTitle("Select Team 2", for: .normal)
        setupTeamButton(team2Button)
        team2Button.addTarget(self, action: #selector(selectTeam2Pressed), for: .touchUpInside)
        selectionCard.addSubview(team2Button)
        
        // Compare button
        compareButton.setTitle("Compare Teams", for: .normal)
        compareButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        compareButton.setTitleColor(.white, for: .normal)
        compareButton.backgroundColor = UIColor.dragonAccent
        compareButton.layer.cornerRadius = 8
        compareButton.isEnabled = false
        compareButton.alpha = 0.6
        compareButton.addTarget(self, action: #selector(comparePressed), for: .touchUpInside)
        selectionCard.addSubview(compareButton)
    }
    
    private func setupTeamButton(_ button: UIButton) {
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.setTitleColor(UIColor.dragonAccent, for: .normal)
        button.backgroundColor = UIColor.dragonSecondaryBackground
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.dragonAccent.cgColor
    }
    
    private func setupComparisonCard() {
        comparisonCard.backgroundColor = UIColor.dragonCardBackground
        comparisonCard.layer.cornerRadius = 12
        comparisonCard.layer.borderWidth = 1
        comparisonCard.layer.borderColor = UIColor.dragonBorder.cgColor
        comparisonCard.isHidden = true
        contentView.addSubview(comparisonCard)
        
        comparisonLabel.text = "Comparison Results"
        comparisonLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        comparisonLabel.textColor = UIColor.dragonTextPrimary
        comparisonCard.addSubview(comparisonLabel)
        
        comparisonStackView.axis = .vertical
        comparisonStackView.spacing = 16
        comparisonStackView.distribution = .fillEqually
        comparisonCard.addSubview(comparisonStackView)
    }
    
    private func setupChartCard() {
        chartCard.backgroundColor = UIColor.dragonCardBackground
        chartCard.layer.cornerRadius = 12
        chartCard.layer.borderWidth = 1
        chartCard.layer.borderColor = UIColor.dragonBorder.cgColor
        chartCard.isHidden = true
        contentView.addSubview(chartCard)
        
        chartLabel.text = "Performance Comparison Chart"
        chartLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        chartLabel.textColor = UIColor.dragonTextPrimary
        chartCard.addSubview(chartLabel)
        
        barChartView.backgroundColor = .clear
        chartCard.addSubview(barChartView)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        selectionCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(180)
        }
        
        selectionLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        team1Button.snp.makeConstraints { make in
            make.top.equalTo(selectionLabel.snp.bottom).offset(16)
            make.leading.equalToSuperview().offset(16)
            make.trailing.equalTo(selectionCard.snp.centerX).offset(-8)
            make.height.equalTo(44)
        }
        
        team2Button.snp.makeConstraints { make in
            make.top.equalTo(selectionLabel.snp.bottom).offset(16)
            make.leading.equalTo(selectionCard.snp.centerX).offset(8)
            make.trailing.equalToSuperview().offset(-16)
            make.height.equalTo(44)
        }
        
        compareButton.snp.makeConstraints { make in
            make.top.equalTo(team1Button.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(150)
            make.height.equalTo(44)
        }
        
        comparisonCard.snp.makeConstraints { make in
            make.top.equalTo(selectionCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(400) // 增加高度避免拥挤
        }
        
        comparisonLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        comparisonStackView.snp.makeConstraints { make in
            make.top.equalTo(comparisonLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }
        
        chartCard.snp.makeConstraints { make in
            make.top.equalTo(comparisonCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(250)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        chartLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        barChartView.snp.makeConstraints { make in
            make.top.equalTo(chartLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Data Loading
    
    private func loadData() {
        // Calculate team statistics
        for race in races {
            for team in race.teams {
                if teamStatistics[team.name] == nil {
                    teamStatistics[team.name] = TeamStatistics(name: team.name)
                }
                teamStatistics[team.name]?.addRace(team: team)
            }
        }
        
        // Get available teams sorted by number of races
        availableTeams = teamStatistics.keys.sorted { teamName1, teamName2 in
            let races1 = teamStatistics[teamName1]?.totalRaces ?? 0
            let races2 = teamStatistics[teamName2]?.totalRaces ?? 0
            return races1 > races2
        }
    }
    
    // MARK: - Actions
    
    @objc private func selectTeam1Pressed() {
        showTeamSelector(for: 1)
    }
    
    @objc private func selectTeam2Pressed() {
        showTeamSelector(for: 2)
    }
    
    @objc private func comparePressed() {
        guard let team1 = selectedTeam1,
              let team2 = selectedTeam2,
              let stats1 = teamStatistics[team1],
              let stats2 = teamStatistics[team2] else { return }
        
        performComparison(team1: stats1, team2: stats2)
    }
    
    private func showTeamSelector(for teamNumber: Int) {
        let alert = UIAlertController(title: "Select Team \(teamNumber)", message: "Choose a team to compare", preferredStyle: .actionSheet)
        
        for teamName in availableTeams {
            // Don't allow selecting the same team twice
            if (teamNumber == 1 && teamName == selectedTeam2) ||
               (teamNumber == 2 && teamName == selectedTeam1) {
                continue
            }
            
            let raceCount = teamStatistics[teamName]?.totalRaces ?? 0
            alert.addAction(UIAlertAction(title: "\(teamName) (\(raceCount) races)", style: .default) { _ in
                if teamNumber == 1 {
                    self.selectedTeam1 = teamName
                    self.team1Button.setTitle(teamName, for: .normal)
                } else {
                    self.selectedTeam2 = teamName
                    self.team2Button.setTitle(teamName, for: .normal)
                }
                self.updateCompareButton()
            })
        }
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            let sourceButton = teamNumber == 1 ? team1Button : team2Button
            popover.sourceView = sourceButton
            popover.sourceRect = sourceButton.bounds
        }
        
        present(alert, animated: true)
    }
    
    private func updateCompareButton() {
        let canCompare = selectedTeam1 != nil && selectedTeam2 != nil
        compareButton.isEnabled = canCompare
        compareButton.alpha = canCompare ? 1.0 : 0.6
    }
    
    private func performComparison(team1: TeamStatistics, team2: TeamStatistics) {
        // Clear existing comparison
        comparisonStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add comparison rows with fixed heights
        let row1 = createComparisonRow("Total Races", "\(team1.totalRaces)", "\(team2.totalRaces)")
        row1.snp.makeConstraints { make in
            make.height.equalTo(32)
        }
        comparisonStackView.addArrangedSubview(row1)

        if let avg1 = team1.averageTime, let avg2 = team2.averageTime {
            let row2 = createComparisonRow("Average Time", TimeFormatter.formatTime(avg1), TimeFormatter.formatTime(avg2))
            row2.snp.makeConstraints { make in
                make.height.equalTo(32)
            }
            comparisonStackView.addArrangedSubview(row2)
        }

        if let best1 = team1.bestTime, let best2 = team2.bestTime {
            let row3 = createComparisonRow("Best Time", TimeFormatter.formatTime(best1), TimeFormatter.formatTime(best2))
            row3.snp.makeConstraints { make in
                make.height.equalTo(32)
            }
            comparisonStackView.addArrangedSubview(row3)
        }

        let row4 = createComparisonRow("Win Rate", String(format: "%.1f%%", team1.winRate * 100), String(format: "%.1f%%", team2.winRate * 100))
        row4.snp.makeConstraints { make in
            make.height.equalTo(32)
        }
        comparisonStackView.addArrangedSubview(row4)

        let row5 = createComparisonRow("Completion Rate", String(format: "%.1f%%", team1.completionRate * 100), String(format: "%.1f%%", team2.completionRate * 100))
        row5.snp.makeConstraints { make in
            make.height.equalTo(32)
        }
        comparisonStackView.addArrangedSubview(row5)

        // Update chart
        updateChart(team1: team1, team2: team2)

        // Show results
        comparisonCard.isHidden = false
        chartCard.isHidden = false
    }
    
    private func createComparisonRow(_ metric: String, _ value1: String, _ value2: String) -> UIView {
        let container = UIView()
        
        let metricLabel = UILabel()
        metricLabel.text = metric
        metricLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        metricLabel.textColor = UIColor.dragonTextSecondary
        container.addSubview(metricLabel)
        
        let value1Label = UILabel()
        value1Label.text = value1
        value1Label.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        value1Label.textColor = UIColor.dragonAccent
        value1Label.textAlignment = .center
        container.addSubview(value1Label)
        
        let vsLabel = UILabel()
        vsLabel.text = "vs"
        vsLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        vsLabel.textColor = UIColor.dragonTextTertiary
        vsLabel.textAlignment = .center
        container.addSubview(vsLabel)
        
        let value2Label = UILabel()
        value2Label.text = value2
        value2Label.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        value2Label.textColor = UIColor.dragonAccentSecondary
        value2Label.textAlignment = .center
        container.addSubview(value2Label)
        
        metricLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.equalTo(100)
            make.height.equalTo(24)
        }

        value1Label.snp.makeConstraints { make in
            make.leading.equalTo(metricLabel.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.width.equalTo(80)
            make.height.equalTo(24)
        }

        vsLabel.snp.makeConstraints { make in
            make.leading.equalTo(value1Label.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.width.equalTo(30)
            make.height.equalTo(24)
        }

        value2Label.snp.makeConstraints { make in
            make.leading.equalTo(vsLabel.snp.trailing).offset(8)
            make.centerY.equalToSuperview()
            make.width.equalTo(80)
            make.height.equalTo(24)
        }
        
        return container
    }
    
    private func updateChart(team1: TeamStatistics, team2: TeamStatistics) {
        // Prepare data for comparison chart
        let categories = ["Win Rate", "Completion Rate", "Avg Rank"]

        let team1Data = [
            team1.winRate * 100,
            team1.completionRate * 100,
            100 - (team1.averageRank ?? 10) * 10 // Convert rank to score (lower rank = higher score)
        ]

        let team2Data = [
            team2.winRate * 100,
            team2.completionRate * 100,
            100 - (team2.averageRank ?? 10) * 10
        ]

        // Create chart model
        let chartModel = AAChartModel()
            .chartType(.column)
            .title("Team Performance Comparison")
            .subtitle("\(team1.name) vs \(team2.name)")
            .backgroundColor("#1C1C1E")
            .dataLabelsEnabled(true)
            .yAxisTitle("Performance Score")
            .categories(categories)
            .series([
                AASeriesElement()
                    .name(team1.name)
                    .data(team1Data)
                    .color("#007AFF"),
                AASeriesElement()
                    .name(team2.name)
                    .data(team2Data)
                    .color("#FF9500")
            ])
            .yAxisGridLineWidth(0.5)
            
            .xAxisGridLineWidth(0.5)
            
            .legendEnabled(true)

        barChartView.aa_drawChartWithChartModel(chartModel)
    }
}
