//
//  PaceCalculatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class PaceCalculatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    
    // Basic Parameters Section
    private let basicParametersCard = UIView()
    private let basicParametersLabel = UILabel()
    private let strategyNameTextField = UITextField()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    private let strokeDistanceTextField = UITextField()
    
    // Strategy Type Section
    private let strategyTypeCard = UIView()
    private let strategyTypeLabel = UILabel()
    private let strategyTypeSegmentedControl = UISegmentedControl(items: [])
    private let strategyDescriptionLabel = UILabel()
    
    // Phases Section
    private let phasesCard = UIView()
    private let phasesLabel = UILabel()
    private let phasesStackView = UIStackView()
    private let addPhaseButton = UIButton(type: .system)
    
    // Results Section
    private let resultsCard = UIView()
    private let resultsLabel = UILabel()
    private let totalStrokesLabel = UILabel()
    private let averageStrokeRateLabel = UILabel()
    private let averageSpeedLabel = UILabel()
    private let validationLabel = UILabel()
    
    // Action Buttons
    private let generateButton = UIButton(type: .system)
    private let saveButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private var strategy: PaceStrategy?
    private let strategyManager = StrategyManager.shared
    private var phaseViews: [RhythmPhaseView] = []
    private let isEditing: Bool

    // MARK: - Initialization

    init(strategy: PaceStrategy? = nil) {
        self.strategy = strategy
        self.isEditing = strategy != nil
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadStrategyData()
        updateResults()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = isEditing ? "Edit Strategy" : "Pace Calculator"
        
        // Navigation
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelPressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.keyboardDismissMode = .onDrag
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = isEditing ? "Edit Strategy Parameters" : "Create New Strategy"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        setupBasicParametersSection()
        setupStrategyTypeSection()
        setupPhasesSection()
        setupResultsSection()
        setupActionButtons()
    }
    
    private func setupBasicParametersSection() {
        basicParametersCard.backgroundColor = UIColor.dragonCardBackground
        basicParametersCard.layer.cornerRadius = 12
        basicParametersCard.layer.borderWidth = 1
        basicParametersCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(basicParametersCard)
        
        basicParametersLabel.text = "Basic Parameters"
        basicParametersLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        basicParametersLabel.textColor = UIColor.dragonTextPrimary
        basicParametersCard.addSubview(basicParametersLabel)
        
        // Strategy Name
        strategyNameTextField.placeholder = "Strategy Name"
        setupTextField(strategyNameTextField)
        basicParametersCard.addSubview(strategyNameTextField)
        
        // Distance
        distanceTextField.placeholder = "Race Distance (meters)"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        basicParametersCard.addSubview(distanceTextField)
        
        // Target Time
        targetTimeTextField.placeholder = "Target Time (MM:SS)"
        setupTextField(targetTimeTextField)
        basicParametersCard.addSubview(targetTimeTextField)
        
        // Stroke Distance
        strokeDistanceTextField.placeholder = "Stroke Distance (meters)"
        strokeDistanceTextField.keyboardType = .decimalPad
        setupTextField(strokeDistanceTextField)
        basicParametersCard.addSubview(strokeDistanceTextField)
        
        // Add text field change listeners
        [strategyNameTextField, distanceTextField, targetTimeTextField, strokeDistanceTextField].forEach { textField in
            textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        }
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonSecondaryBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupStrategyTypeSection() {
        strategyTypeCard.backgroundColor = UIColor.dragonCardBackground
        strategyTypeCard.layer.cornerRadius = 12
        strategyTypeCard.layer.borderWidth = 1
        strategyTypeCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(strategyTypeCard)
        
        strategyTypeLabel.text = "Strategy Type"
        strategyTypeLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        strategyTypeLabel.textColor = UIColor.dragonTextPrimary
        strategyTypeCard.addSubview(strategyTypeLabel)
        
        // Setup segmented control
        let templateTypes = StrategyTemplate.getAvailableTemplates()
        let items = templateTypes.map { $0.displayName } + ["Custom"]
        
        strategyTypeSegmentedControl.removeAllSegments()
        for (index, item) in items.enumerated() {
            strategyTypeSegmentedControl.insertSegment(withTitle: item, at: index, animated: false)
        }
        strategyTypeSegmentedControl.selectedSegmentIndex = items.count - 1 // Default to Custom
        strategyTypeSegmentedControl.backgroundColor = UIColor.dragonSecondaryBackground
        strategyTypeSegmentedControl.selectedSegmentTintColor = UIColor.dragonAccent
        strategyTypeSegmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.dragonTextPrimary], for: .normal)
        strategyTypeSegmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        strategyTypeSegmentedControl.addTarget(self, action: #selector(strategyTypeChanged), for: .valueChanged)
        strategyTypeCard.addSubview(strategyTypeSegmentedControl)
        
        strategyDescriptionLabel.text = "Create a custom strategy with your own phases"
        strategyDescriptionLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        strategyDescriptionLabel.textColor = UIColor.dragonTextSecondary
        strategyDescriptionLabel.numberOfLines = 0
        strategyTypeCard.addSubview(strategyDescriptionLabel)
    }
    
    private func setupPhasesSection() {
        phasesCard.backgroundColor = UIColor.dragonCardBackground
        phasesCard.layer.cornerRadius = 12
        phasesCard.layer.borderWidth = 1
        phasesCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(phasesCard)
        
        phasesLabel.text = "Race Phases"
        phasesLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        phasesLabel.textColor = UIColor.dragonTextPrimary
        phasesCard.addSubview(phasesLabel)
        
        phasesStackView.axis = .vertical
        phasesStackView.spacing = 12
        phasesStackView.distribution = .fill
        phasesCard.addSubview(phasesStackView)
        
        addPhaseButton.setTitle("+ Add Phase", for: .normal)
        addPhaseButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        addPhaseButton.setTitleColor(UIColor.dragonAccent, for: .normal)
        addPhaseButton.backgroundColor = UIColor.dragonSecondaryBackground
        addPhaseButton.layer.cornerRadius = 8
        addPhaseButton.layer.borderWidth = 1
        addPhaseButton.layer.borderColor = UIColor.dragonAccent.cgColor
        addPhaseButton.addTarget(self, action: #selector(addPhasePressed), for: .touchUpInside)
        phasesCard.addSubview(addPhaseButton)
    }
    
    private func setupResultsSection() {
        resultsCard.backgroundColor = UIColor.dragonCardBackground
        resultsCard.layer.cornerRadius = 12
        resultsCard.layer.borderWidth = 1
        resultsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(resultsCard)
        
        resultsLabel.text = "Strategy Analysis"
        resultsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        resultsLabel.textColor = UIColor.dragonTextPrimary
        resultsCard.addSubview(resultsLabel)
        
        totalStrokesLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        totalStrokesLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(totalStrokesLabel)
        
        averageStrokeRateLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        averageStrokeRateLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(averageStrokeRateLabel)
        
        averageSpeedLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        averageSpeedLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(averageSpeedLabel)
        
        validationLabel.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        resultsCard.addSubview(validationLabel)
    }
    
    private func setupActionButtons() {
        generateButton.setTitle("Generate Strategy", for: .normal)
        generateButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        generateButton.setTitleColor(.white, for: .normal)
        generateButton.backgroundColor = UIColor.dragonAccentSecondary
        generateButton.layer.cornerRadius = 12
        generateButton.addTarget(self, action: #selector(generateStrategyPressed), for: .touchUpInside)
        contentView.addSubview(generateButton)
        
        saveButton.setTitle(isEditing ? "Update Strategy" : "Save Strategy", for: .normal)
        saveButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        saveButton.setTitleColor(.white, for: .normal)
        saveButton.backgroundColor = UIColor.dragonAccent
        saveButton.layer.cornerRadius = 12
        saveButton.addTarget(self, action: #selector(saveStrategyPressed), for: .touchUpInside)
        contentView.addSubview(saveButton)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Basic Parameters Card
        basicParametersCard.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        basicParametersLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        strategyNameTextField.snp.makeConstraints { make in
            make.top.equalTo(basicParametersLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(strategyNameTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        strokeDistanceTextField.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Strategy Type Card
        strategyTypeCard.snp.makeConstraints { make in
            make.top.equalTo(basicParametersCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        strategyTypeLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        strategyTypeSegmentedControl.snp.makeConstraints { make in
            make.top.equalTo(strategyTypeLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }

        strategyDescriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(strategyTypeSegmentedControl.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Phases Card
        phasesCard.snp.makeConstraints { make in
            make.top.equalTo(strategyTypeCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        phasesLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        phasesStackView.snp.makeConstraints { make in
            make.top.equalTo(phasesLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        addPhaseButton.snp.makeConstraints { make in
            make.top.equalTo(phasesStackView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Results Card
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(phasesCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        resultsLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        totalStrokesLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        averageStrokeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(totalStrokesLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        averageSpeedLabel.snp.makeConstraints { make in
            make.top.equalTo(averageStrokeRateLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        validationLabel.snp.makeConstraints { make in
            make.top.equalTo(averageSpeedLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Action Buttons
        generateButton.snp.makeConstraints { make in
            make.top.equalTo(resultsCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
        }

        saveButton.snp.makeConstraints { make in
            make.top.equalTo(generateButton.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Data Loading

    private func loadStrategyData() {
        guard let strategy = strategy else {
            // Set default values for new strategy
            distanceTextField.text = "500"
            targetTimeTextField.text = "2:00"
            strokeDistanceTextField.text = "2.5"
            addDefaultPhase()
            return
        }

        // Load existing strategy data
        strategyNameTextField.text = strategy.name
        distanceTextField.text = String(Int(strategy.raceDistance))
        targetTimeTextField.text = TimeFormatter.formatTime(strategy.targetTime)
        strokeDistanceTextField.text = String(strategy.averageStrokeDistance)

        // Set strategy type
        let templateTypes = StrategyTemplate.getAvailableTemplates()
        if let index = templateTypes.firstIndex(of: strategy.strategyType) {
            strategyTypeSegmentedControl.selectedSegmentIndex = index
        } else {
            strategyTypeSegmentedControl.selectedSegmentIndex = templateTypes.count // Custom
        }
        updateStrategyDescription()

        // Load phases
        for phase in strategy.phases {
            addPhaseView(with: phase)
        }

        if phaseViews.isEmpty {
            addDefaultPhase()
        }
    }

    private func addDefaultPhase() {
        let phase = RhythmPhase(
            name: "Main Phase",
            duration: 120, // 2 minutes
            strokeRate: 60, // 60 SPM
            distance: 500, // 500m
            strokeDistance: 2.5
        )
        addPhaseView(with: phase)
    }

    // MARK: - Actions

    @objc private func cancelPressed() {
        dismiss(animated: true)
    }

    @objc private func textFieldDidChange() {
        updateResults()
    }

    @objc private func strategyTypeChanged() {
        updateStrategyDescription()

        // If not custom, generate template strategy
        if strategyTypeSegmentedControl.selectedSegmentIndex < StrategyTemplate.getAvailableTemplates().count {
            generateTemplateStrategy()
        }
    }

    @objc private func addPhasePressed() {
        let phase = RhythmPhase(
            name: "Phase \(phaseViews.count + 1)",
            duration: 30,
            strokeRate: 60,
            distance: 100,
            strokeDistance: getCurrentStrokeDistance()
        )
        addPhaseView(with: phase)
        updateResults()
    }

    @objc private func generateStrategyPressed() {
        generateTemplateStrategy()
    }

    @objc private func saveStrategyPressed() {
        guard validateInputs() else { return }

        let strategyName = strategyNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? "Untitled Strategy"
        let distance = getCurrentDistance()
        let targetTime = getCurrentTargetTime()
        let strokeDistance = getCurrentStrokeDistance()

        if let existingStrategy = strategy {
            // Update existing strategy
            existingStrategy.name = strategyName
            existingStrategy.raceDistance = distance
            existingStrategy.targetTime = targetTime
            existingStrategy.averageStrokeDistance = strokeDistance

            // Clear existing phases and add new ones
            existingStrategy.phases.removeAll()
            for phaseView in phaseViews {
                existingStrategy.addPhase(phaseView.phase)
            }

            strategyManager.saveStrategy(existingStrategy)
        } else {
            // Create new strategy
            let newStrategy = strategyManager.createStrategy(
                name: strategyName,
                distance: distance,
                targetTime: targetTime,
                strokeDistance: strokeDistance,
                type: getCurrentStrategyType()
            )

            // Clear default phases and add custom ones
            newStrategy.phases.removeAll()
            for phaseView in phaseViews {
                newStrategy.addPhase(phaseView.phase)
            }

            strategyManager.saveStrategy(newStrategy)
        }

        dismiss(animated: true)
    }

    // MARK: - Helper Methods

    private func getCurrentDistance() -> Double {
        return Double(distanceTextField.text ?? "500") ?? 500.0
    }

    private func getCurrentTargetTime() -> TimeInterval {
        guard let timeText = targetTimeTextField.text else { return 120.0 }
        return TimeFormatter.parseTime(timeText) ?? 120.0
    }

    private func getCurrentStrokeDistance() -> Double {
        return Double(strokeDistanceTextField.text ?? "2.5") ?? 2.5
    }

    private func getCurrentStrategyType() -> StrategyType {
        let templateTypes = StrategyTemplate.getAvailableTemplates()
        let selectedIndex = strategyTypeSegmentedControl.selectedSegmentIndex

        if selectedIndex < templateTypes.count {
            return templateTypes[selectedIndex]
        } else {
            return .custom
        }
    }

    private func validateInputs() -> Bool {
        guard ((strategyNameTextField.text?.isEmpty) == nil) else {
            showAlert(title: "Invalid Name", message: "Please enter a strategy name.")
            return false
        }

        guard getCurrentDistance() > 0 else {
            showAlert(title: "Invalid Distance", message: "Please enter a valid race distance.")
            return false
        }

        guard getCurrentTargetTime() > 0 else {
            showAlert(title: "Invalid Time", message: "Please enter a valid target time.")
            return false
        }

        guard getCurrentStrokeDistance() > 0 else {
            showAlert(title: "Invalid Stroke Distance", message: "Please enter a valid stroke distance.")
            return false
        }

        guard !phaseViews.isEmpty else {
            showAlert(title: "No Phases", message: "Please add at least one race phase.")
            return false
        }

        return true
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    // MARK: - Phase Management

    private func addPhaseView(with phase: RhythmPhase) {
        let phaseView = RhythmPhaseView(phase: phase)
        phaseView.delegate = self
        phaseViews.append(phaseView)
        phasesStackView.addArrangedSubview(phaseView)
    }

    private func removePhaseView(_ phaseView: RhythmPhaseView) {
        guard let index = phaseViews.firstIndex(of: phaseView) else { return }

        phaseViews.remove(at: index)
        phasesStackView.removeArrangedSubview(phaseView)
        phaseView.removeFromSuperview()

        updateResults()
    }

    // MARK: - Strategy Generation

    private func generateTemplateStrategy() {
        guard validateBasicInputs() else { return }

        let distance = getCurrentDistance()
        let targetTime = getCurrentTargetTime()
        let strokeDistance = getCurrentStrokeDistance()
        let strategyType = getCurrentStrategyType()

        // Clear existing phases
        for phaseView in phaseViews {
            phasesStackView.removeArrangedSubview(phaseView)
            phaseView.removeFromSuperview()
        }
        phaseViews.removeAll()

        // Generate template strategy
        let templateStrategy = StrategyTemplate.createStrategy(
            type: strategyType,
            distance: distance,
            targetTime: targetTime,
            strokeDistance: strokeDistance
        )

        // Add phases from template
        for phase in templateStrategy.phases {
            addPhaseView(with: phase)
        }

        updateResults()
    }

    private func validateBasicInputs() -> Bool {
        return getCurrentDistance() > 0 && getCurrentTargetTime() > 0 && getCurrentStrokeDistance() > 0
    }

    // MARK: - UI Updates

    private func updateStrategyDescription() {
        let strategyType = getCurrentStrategyType()
        strategyDescriptionLabel.text = strategyType.description
    }

    private func updateResults() {
        guard validateBasicInputs() else {
            clearResults()
            return
        }

        let totalStrokes = phaseViews.reduce(0) { $0 + $1.phase.totalStrokes }
        let totalDuration = phaseViews.reduce(0.0) { $0 + $1.phase.duration }
        let totalDistance = phaseViews.reduce(0.0) { $0 + $1.phase.distance }

        let averageStrokeRate = totalDuration > 0 ?
            phaseViews.reduce(0.0) { $0 + ($1.phase.strokeRate * $1.phase.duration) } / totalDuration : 0

        let averageSpeed = totalDuration > 0 ? totalDistance / totalDuration : 0

        totalStrokesLabel.text = "Total Strokes: \(totalStrokes)"
        averageStrokeRateLabel.text = "Average Stroke Rate: \(Int(averageStrokeRate)) SPM"
        averageSpeedLabel.text = "Average Speed: \(String(format: "%.1f", averageSpeed * 3.6)) km/h"

        // Validation
        let targetDistance = getCurrentDistance()
        let targetTime = getCurrentTargetTime()

        let distanceDiff = abs(totalDistance - targetDistance)
        let timeDiff = abs(totalDuration - targetTime)

        if distanceDiff < 1.0 && timeDiff < 1.0 {
            validationLabel.text = "✅ Strategy is valid"
            validationLabel.textColor = UIColor.dragonSuccess
        } else {
            validationLabel.text = "⚠️ Strategy needs adjustment (Distance: \(String(format: "%.0f", distanceDiff))m, Time: \(String(format: "%.0f", timeDiff))s)"
            validationLabel.textColor = UIColor.dragonWarning
        }
    }

    private func clearResults() {
        totalStrokesLabel.text = "Total Strokes: --"
        averageStrokeRateLabel.text = "Average Stroke Rate: -- SPM"
        averageSpeedLabel.text = "Average Speed: -- km/h"
        validationLabel.text = "Enter parameters to see analysis"
        validationLabel.textColor = UIColor.dragonTextTertiary
    }
}

// MARK: - RhythmPhaseViewDelegate

extension PaceCalculatorViewController: RhythmPhaseViewDelegate {
    func rhythmPhaseViewDidChange(_ phaseView: RhythmPhaseView) {
        updateResults()
    }

    func rhythmPhaseViewDidRequestRemoval(_ phaseView: RhythmPhaseView) {
        guard phaseViews.count > 1 else {
            showAlert(title: "Minimum Phases", message: "You need at least one phase for a strategy.")
            return
        }

        removePhaseView(phaseView)
    }
}
