//
//  StrategyTemplateGenerator.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

class StrategyTemplateGenerator {
    
    // 常见比赛距离
    static let commonDistances: [Double] = [200, 500, 1000, 2000]
    
    // 生成均速型策略
    static func createEvenPaceStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "均速型策略 - \(Int(distance))m",
            strategyType: .evenPace,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        // 计算平均速度和划频
        let averageSpeed = distance / targetTime
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // 创建三个阶段，保持相对稳定的划频
        let startPhase = RacePhase(
            name: "起步阶段",
            phaseType: .start,
            duration: targetTime * 0.2,
            distance: distance * 0.2,
            targetStrokeRate: averageStrokeRate + 5, // 起步稍快
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "中段阶段",
            phaseType: .middle,
            duration: targetTime * 0.6,
            distance: distance * 0.6,
            targetStrokeRate: averageStrokeRate,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "冲刺阶段",
            phaseType: .sprint,
            duration: targetTime * 0.2,
            distance: distance * 0.2,
            targetStrokeRate: averageStrokeRate + 3, // 冲刺稍快
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // 生成起步爆发型策略
    static func createStartBurstStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "起步爆发型策略 - \(Int(distance))m",
            strategyType: .startBurst,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // 起步高频，中段稳定，冲刺适度提升
        let startPhase = RacePhase(
            name: "爆发起步",
            phaseType: .start,
            duration: targetTime * 0.15,
            distance: distance * 0.2, // 起步阶段距离比例更大
            targetStrokeRate: averageStrokeRate + 15, // 高频起步
            strokeDistance: strokeDistance,
            allowsSubstitution: true
        )
        startPhase.substitutionTiming = startPhase.duration * 0.8 // 起步后期换人
        
        let middlePhase = RacePhase(
            name: "稳定中段",
            phaseType: .middle,
            duration: targetTime * 0.65,
            distance: distance * 0.65,
            targetStrokeRate: averageStrokeRate - 3, // 中段稍慢恢复
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "终点冲刺",
            phaseType: .sprint,
            duration: targetTime * 0.2,
            distance: distance * 0.15,
            targetStrokeRate: averageStrokeRate + 8,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // 生成冲刺终结型策略
    static func createFinishSprintStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "冲刺终结型策略 - \(Int(distance))m",
            strategyType: .finishSprint,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // 前段保守，后段爆发
        let startPhase = RacePhase(
            name: "稳健起步",
            phaseType: .start,
            duration: targetTime * 0.25,
            distance: distance * 0.25,
            targetStrokeRate: averageStrokeRate - 5,
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "蓄力中段",
            phaseType: .middle,
            duration: targetTime * 0.5,
            distance: distance * 0.5,
            targetStrokeRate: averageStrokeRate - 2,
            strokeDistance: strokeDistance,
            allowsSubstitution: true
        )
        middlePhase.substitutionTiming = middlePhase.duration * 0.7 // 中段后期换人准备冲刺
        
        let sprintPhase = RacePhase(
            name: "全力冲刺",
            phaseType: .sprint,
            duration: targetTime * 0.25,
            distance: distance * 0.25,
            targetStrokeRate: averageStrokeRate + 12, // 大幅提升划频
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // 生成负分段策略（后程加速）
    static func createNegativeSplitStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "负分段策略 - \(Int(distance))m",
            strategyType: .negative,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // 逐渐加速的策略
        let startPhase = RacePhase(
            name: "保守起步",
            phaseType: .start,
            duration: targetTime * 0.3,
            distance: distance * 0.3,
            targetStrokeRate: averageStrokeRate - 8,
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "逐步加速",
            phaseType: .middle,
            duration: targetTime * 0.4,
            distance: distance * 0.4,
            targetStrokeRate: averageStrokeRate,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "强势冲刺",
            phaseType: .sprint,
            duration: targetTime * 0.3,
            distance: distance * 0.3,
            targetStrokeRate: averageStrokeRate + 10,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // 生成正分段策略（前程快速）
    static func createPositiveSplitStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "正分段策略 - \(Int(distance))m",
            strategyType: .positive,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // 前快后稳的策略
        let startPhase = RacePhase(
            name: "快速起步",
            phaseType: .start,
            duration: targetTime * 0.3,
            distance: distance * 0.35,
            targetStrokeRate: averageStrokeRate + 10,
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "稳定维持",
            phaseType: .middle,
            duration: targetTime * 0.4,
            distance: distance * 0.4,
            targetStrokeRate: averageStrokeRate - 2,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "保持冲刺",
            phaseType: .sprint,
            duration: targetTime * 0.3,
            distance: distance * 0.25,
            targetStrokeRate: averageStrokeRate + 3,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // 根据策略类型生成策略
    static func createStrategy(type: StrategyType, distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        switch type {
        case .evenPace:
            return createEvenPaceStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .startBurst:
            return createStartBurstStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .finishSprint:
            return createFinishSprintStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .negative:
            return createNegativeSplitStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .positive:
            return createPositiveSplitStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .custom:
            return createEvenPaceStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        }
    }
    
    // 获取所有可用的策略类型（除了自定义）
    static func getAvailableTemplateTypes() -> [StrategyType] {
        return StrategyType.allCases.filter { $0 != .custom }
    }
}
