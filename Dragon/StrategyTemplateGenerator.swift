//
//  StrategyTemplateGenerator.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

class StrategyTemplateGenerator {
    
    // Common race distances
    static let commonDistances: [Double] = [200, 500, 1000, 2000]
    
    // Generate Even Pace Strategy
    static func createEvenPaceStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "Even Pace Strategy - \(Int(distance))m",
            strategyType: .evenPace,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        // Calculate average speed and stroke rate
        let averageSpeed = distance / targetTime
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // Three phases with relatively stable stroke rate
        let startPhase = RacePhase(
            name: "Start Phase",
            phaseType: .start,
            duration: targetTime * 0.2,
            distance: distance * 0.2,
            targetStrokeRate: averageStrokeRate + 5, // Slightly faster at start
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "Middle Phase",
            phaseType: .middle,
            duration: targetTime * 0.6,
            distance: distance * 0.6,
            targetStrokeRate: averageStrokeRate,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "Sprint Phase",
            phaseType: .sprint,
            duration: targetTime * 0.2,
            distance: distance * 0.2,
            targetStrokeRate: averageStrokeRate + 3, // Slight increase for sprint
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // Generate Start Burst Strategy
    static func createStartBurstStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "Start Burst Strategy - \(Int(distance))m",
            strategyType: .startBurst,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // High stroke rate at start, stable middle, moderate sprint
        let startPhase = RacePhase(
            name: "Explosive Start",
            phaseType: .start,
            duration: targetTime * 0.15,
            distance: distance * 0.2,
            targetStrokeRate: averageStrokeRate + 15,
            strokeDistance: strokeDistance,
            allowsSubstitution: true
        )
        startPhase.substitutionTiming = startPhase.duration * 0.8
        
        let middlePhase = RacePhase(
            name: "Steady Middle",
            phaseType: .middle,
            duration: targetTime * 0.65,
            distance: distance * 0.65,
            targetStrokeRate: averageStrokeRate - 3,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "Final Sprint",
            phaseType: .sprint,
            duration: targetTime * 0.2,
            distance: distance * 0.15,
            targetStrokeRate: averageStrokeRate + 8,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // Generate Finish Sprint Strategy
    static func createFinishSprintStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "Finish Sprint Strategy - \(Int(distance))m",
            strategyType: .finishSprint,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // Conservative start, explosive finish
        let startPhase = RacePhase(
            name: "Controlled Start",
            phaseType: .start,
            duration: targetTime * 0.25,
            distance: distance * 0.25,
            targetStrokeRate: averageStrokeRate - 5,
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "Power Reserve",
            phaseType: .middle,
            duration: targetTime * 0.5,
            distance: distance * 0.5,
            targetStrokeRate: averageStrokeRate - 2,
            strokeDistance: strokeDistance,
            allowsSubstitution: true
        )
        middlePhase.substitutionTiming = middlePhase.duration * 0.7
        
        let sprintPhase = RacePhase(
            name: "Full Sprint",
            phaseType: .sprint,
            duration: targetTime * 0.25,
            distance: distance * 0.25,
            targetStrokeRate: averageStrokeRate + 12,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // Generate Negative Split Strategy
    static func createNegativeSplitStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "Negative Split Strategy - \(Int(distance))m",
            strategyType: .negative,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // Gradual acceleration
        let startPhase = RacePhase(
            name: "Conservative Start",
            phaseType: .start,
            duration: targetTime * 0.3,
            distance: distance * 0.3,
            targetStrokeRate: averageStrokeRate - 8,
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "Gradual Build",
            phaseType: .middle,
            duration: targetTime * 0.4,
            distance: distance * 0.4,
            targetStrokeRate: averageStrokeRate,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "Strong Finish",
            phaseType: .sprint,
            duration: targetTime * 0.3,
            distance: distance * 0.3,
            targetStrokeRate: averageStrokeRate + 10,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // Generate Positive Split Strategy
    static func createPositiveSplitStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        let strategy = RaceStrategy(
            name: "Positive Split Strategy - \(Int(distance))m",
            strategyType: .positive,
            raceDistance: distance,
            targetTime: targetTime
        )
        
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let averageStrokeRate = (Double(totalStrokes) / targetTime) * 60
        
        // Fast start, steady finish
        let startPhase = RacePhase(
            name: "Fast Start",
            phaseType: .start,
            duration: targetTime * 0.3,
            distance: distance * 0.35,
            targetStrokeRate: averageStrokeRate + 10,
            strokeDistance: strokeDistance
        )
        
        let middlePhase = RacePhase(
            name: "Maintain Pace",
            phaseType: .middle,
            duration: targetTime * 0.4,
            distance: distance * 0.4,
            targetStrokeRate: averageStrokeRate - 2,
            strokeDistance: strokeDistance
        )
        
        let sprintPhase = RacePhase(
            name: "Controlled Sprint",
            phaseType: .sprint,
            duration: targetTime * 0.3,
            distance: distance * 0.25,
            targetStrokeRate: averageStrokeRate + 3,
            strokeDistance: strokeDistance
        )
        
        strategy.phases = [startPhase, middlePhase, sprintPhase]
        return strategy
    }
    
    // Generate strategy by type
    static func createStrategy(type: StrategyType, distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> RaceStrategy {
        switch type {
        case .evenPace:
            return createEvenPaceStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .startBurst:
            return createStartBurstStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .finishSprint:
            return createFinishSprintStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .negative:
            return createNegativeSplitStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .positive:
            return createPositiveSplitStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .custom:
            return createEvenPaceStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        }
    }
    
    // Get all available (non-custom) strategy types
    static func getAvailableTemplateTypes() -> [StrategyType] {
        return StrategyType.allCases.filter { $0 != .custom }
    }
}
