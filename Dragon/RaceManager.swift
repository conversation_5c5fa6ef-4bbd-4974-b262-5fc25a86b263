//
//  RaceManager.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Protocol for race manager delegate
protocol RaceManagerDelegate: AnyObject {
    func raceManager(_ manager: Race<PERSON>anager, didUpdateRace race: RaceRecord)
    func raceManager(_ manager: <PERSON><PERSON><PERSON><PERSON>, didFinishTeam team: Team, in race: RaceRecord)
    func raceManager(_ manager: RaceManager, didCompleteRace race: RaceRecord)
}

/// Manages race records and provides persistence
class RaceManager: NSObject {
    
    // MARK: - Properties
    
    /// Shared singleton instance
    static let shared = RaceManager()
    
    /// Delegate for race updates
    weak var delegate: RaceManagerDelegate?
    
    /// All race records
    private(set) var raceRecords: [RaceRecord] = []
    
    /// Currently active race (if any)
    private(set) var currentRace: RaceRecord?
    
    /// Timer for updating race time
    private var raceTimer: Timer?
    
    /// UserDefaults key for storing race records
    private let raceRecordsKey = "DragonRaceRecords"
    
    // MARK: - Initialization
    
    private override init() {
        super.init()
        loadRaceRecords()
        // Add sample data if no races exist
        if completedRaces.isEmpty {
            addSampleData()
        }
    }
    
    // MARK: - Race Management
    
    /// Create a new race
    /// - Parameters:
    ///   - name: Race name (optional)
    ///   - teamNames: Array of team names
    /// - Returns: Created race record
    func createRace(name: String? = nil, teamNames: [String]) -> RaceRecord {
        let teams = teamNames.map { Team(name: $0) }
        let race = RaceRecord(name: name, teams: teams)
        
        raceRecords.append(race)
        currentRace = race
        
        saveRaceRecords()
        delegate?.raceManager(self, didUpdateRace: race)
        
        return race
    }
    
    /// Start the current race
    /// - Returns: True if successful, false if no current race or race already started
    @discardableResult
    func startCurrentRace() -> Bool {
        guard let race = currentRace, race.status == .created else {
            return false
        }
        
        race.startRace()
        startRaceTimer()
        
        saveRaceRecords()
        delegate?.raceManager(self, didUpdateRace: race)
        
        return true
    }
    
    /// Finish a team in the current race
    /// - Parameter team: Team to finish
    /// - Returns: True if successful
    @discardableResult
    func finishTeam(_ team: Team) -> Bool {
        guard let race = currentRace else { return false }
        
        let success = race.finishTeam(team)
        if success {
            saveRaceRecords()
            delegate?.raceManager(self, didFinishTeam: team, in: race)
            delegate?.raceManager(self, didUpdateRace: race)
            
            // Check if race is complete
            race.checkAutoEnd()
            if race.status == .completed {
                completeCurrentRace()
            }
        }
        
        return success
    }
    
    /// End the current race manually
    func endCurrentRace() {
        guard let race = currentRace, race.status == .inProgress else { return }
        
        race.endRace()
        completeCurrentRace()
    }
    
    /// Complete the current race (internal method)
    private func completeCurrentRace() {
        guard let race = currentRace else { return }
        
        stopRaceTimer()
        saveRaceRecords()
        
        delegate?.raceManager(self, didCompleteRace: race)
        delegate?.raceManager(self, didUpdateRace: race)
        
        // Clear current race
        currentRace = nil
    }
    
    /// Delete a race record
    /// - Parameter race: Race to delete
    /// - Returns: True if successful
    @discardableResult
    func deleteRace(_ race: RaceRecord) -> Bool {
        guard let index = raceRecords.firstIndex(of: race) else {
            return false
        }
        
        // If deleting current race, stop it first
        if currentRace == race {
            if race.status == .inProgress {
                endCurrentRace()
            } else {
                currentRace = nil
            }
        }
        
        raceRecords.remove(at: index)
        saveRaceRecords()
        
        return true
    }
    
    /// Set a race as current (for viewing/resuming)
    /// - Parameter race: Race to set as current
    func setCurrentRace(_ race: RaceRecord) {
        currentRace = race
        
        // If race is in progress, start timer
        if race.status == .inProgress {
            startRaceTimer()
        }
    }
    
    // MARK: - Timer Management
    
    /// Start the race timer for live updates
    private func startRaceTimer() {
        stopRaceTimer()
        
        raceTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self, let race = self.currentRace else { return }
            self.delegate?.raceManager(self, didUpdateRace: race)
        }
    }
    
    /// Stop the race timer
    private func stopRaceTimer() {
        raceTimer?.invalidate()
        raceTimer = nil
    }
    
    // MARK: - Persistence
    
    /// Save race records to UserDefaults
    private func saveRaceRecords() {
        do {
            let data = try JSONEncoder().encode(raceRecords)
            UserDefaults.standard.set(data, forKey: raceRecordsKey)
        } catch {
            print("Failed to save race records: \(error)")
        }
    }

    // MARK: - Sample Data

    private func addSampleData() {
        // Create sample teams
        let teams1 = [
            Team(name: "Dragon Warriors"),
            Team(name: "Thunder Bolts"),
            Team(name: "Wave Riders")
        ]

        let teams2 = [
            Team(name: "Dragon Warriors"),
            Team(name: "Thunder Bolts"),
            Team(name: "Fire Dragons")
        ]

        let teams3 = [
            Team(name: "Wave Riders"),
            Team(name: "Fire Dragons"),
            Team(name: "Storm Chasers")
        ]

        // Create sample races
        let race1 = RaceRecord(name: "Spring Championship", teams: teams1)
        let race2 = RaceRecord(name: "Summer Cup", teams: teams2)
        let race3 = RaceRecord(name: "Autumn Classic", teams: teams3)

        // Simulate race results by setting finish times directly
        // Race 1 results
        race1.teams[0].finishTime = 125.5  // Dragon Warriors - 1st
        race1.teams[0].rank = 1

        race1.teams[1].finishTime = 128.2  // Thunder Bolts - 2nd
        race1.teams[1].rank = 2

        race1.teams[2].finishTime = 132.1  // Wave Riders - 3rd
        race1.teams[2].rank = 3

        // Race 2 results
        race2.teams[1].finishTime = 245.8  // Thunder Bolts - 1st
        race2.teams[1].rank = 1

        race2.teams[0].finishTime = 248.3  // Dragon Warriors - 2nd
        race2.teams[0].rank = 2

        race2.teams[2].finishTime = 252.7  // Fire Dragons - 3rd
        race2.teams[2].rank = 3

        // Race 3 results
        race3.teams[2].finishTime = 185.2  // Storm Chasers - 1st
        race3.teams[2].rank = 1

        race3.teams[0].finishTime = 187.9  // Wave Riders - 2nd
        race3.teams[0].rank = 2

        race3.teams[1].finishTime = 191.5  // Fire Dragons - 3rd
        race3.teams[1].rank = 3

        // Mark races as completed
        race1.status = RaceStatus.completed
        race2.status = RaceStatus.completed
        race3.status = RaceStatus.completed

        // Add to records
        raceRecords.append(contentsOf: [race1, race2, race3])
        saveRaceRecords()
    }

    /// Load race records from UserDefaults
    private func loadRaceRecords() {
        guard let data = UserDefaults.standard.data(forKey: raceRecordsKey) else {
            return
        }
        
        do {
            raceRecords = try JSONDecoder().decode([RaceRecord].self, from: data)
        } catch {
            print("Failed to load race records: \(error)")
            raceRecords = []
        }
    }
    
    // MARK: - Computed Properties
    
    /// Get completed races sorted by date (newest first)
    var completedRaces: [RaceRecord] {
        return raceRecords
            .filter { $0.status == .completed }
            .sorted { $0.createdAt > $1.createdAt }
    }
    
    /// Get current race elapsed time
    var currentRaceElapsedTime: TimeInterval? {
        guard let race = currentRace,
              let startTime = race.startTime,
              race.status == .inProgress else {
            return nil
        }
        return Date().timeIntervalSince(startTime)
    }
    
    /// Check if there's an active race
    var hasActiveRace: Bool {
        return currentRace?.status == .inProgress
    }
}

// MARK: - Equatable for RaceRecord

extension RaceRecord {
    override func isEqual(_ object: Any?) -> Bool {
        guard let other = object as? RaceRecord else { return false }
        return id == other.id
    }
    
    override var hash: Int {
        return id.hashValue
    }
}
