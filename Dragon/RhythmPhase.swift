//
//  RhythmPhase.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Represents a phase in a dragon boat race strategy
class RhythmPhase: NSObject, Codable {
    
    // MARK: - Properties
    
    /// Unique identifier for the phase
    let id: UUID
    
    /// Name of the phase (e.g., "Start", "Middle", "Sprint")
    var name: String
    
    /// Duration of this phase in seconds
    var duration: TimeInterval
    
    /// Target stroke rate for this phase (strokes per minute)
    var strokeRate: Double
    
    /// Distance covered in this phase (meters)
    var distance: Double
    
    /// Average stroke distance (meters per stroke)
    var strokeDistance: Double
    
    /// Percentage of total race time this phase represents
    var timePercentage: Double
    
    /// Phase type for categorization
    var phaseType: PhaseType
    
    /// Whether this phase allows crew substitution
    var allowsSubstitution: Bool
    
    /// Recommended substitution timing within this phase (0.0 to 1.0)
    var substitutionTiming: Double?
    
    // MARK: - Computed Properties
    
    /// Total number of strokes in this phase
    var totalStrokes: Int {
        return Int(ceil(distance / strokeDistance))
    }
    
    /// Average speed during this phase (m/s)
    var averageSpeed: Double {
        return distance / duration
    }
    
    /// Strokes per second
    var strokesPerSecond: Double {
        return strokeRate / 60.0
    }
    
    // MARK: - Initialization
    
    init(name: String, 
         duration: TimeInterval, 
         strokeRate: Double, 
         distance: Double, 
         strokeDistance: Double = 2.5,
         phaseType: PhaseType = .middle,
         allowsSubstitution: Bool = false) {
        
        self.id = UUID()
        self.name = name
        self.duration = duration
        self.strokeRate = strokeRate
        self.distance = distance
        self.strokeDistance = strokeDistance
        self.phaseType = phaseType
        self.allowsSubstitution = allowsSubstitution
        self.timePercentage = 0.0 // Will be calculated when added to strategy
        super.init()
    }
    
    // MARK: - Methods
    
    /// Calculate optimal stroke rate based on target speed
    /// - Parameter targetSpeed: Desired speed in m/s
    func calculateOptimalStrokeRate(for targetSpeed: Double) {
        let requiredStrokesPerSecond = targetSpeed / strokeDistance
        self.strokeRate = requiredStrokesPerSecond * 60.0
    }
    
    /// Update distance based on duration and stroke rate
    func updateDistanceFromTiming() {
        let totalStrokes = (strokeRate / 60.0) * duration
        self.distance = totalStrokes * strokeDistance
    }
    
    /// Update duration based on distance and stroke rate
    func updateDurationFromDistance() {
        let totalStrokes = distance / strokeDistance
        self.duration = totalStrokes / (strokeRate / 60.0)
    }
    
    /// Get formatted description of the phase
    func getDescription() -> String {
        let speedKmh = averageSpeed * 3.6
        return "\(name): \(Int(duration))s, \(Int(strokeRate)) SPM, \(String(format: "%.1f", speedKmh)) km/h"
    }
    
    // MARK: - Codable
    
    enum CodingKeys: String, CodingKey {
        case id, name, duration, strokeRate, distance, strokeDistance
        case timePercentage, phaseType, allowsSubstitution, substitutionTiming
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        duration = try container.decode(TimeInterval.self, forKey: .duration)
        strokeRate = try container.decode(Double.self, forKey: .strokeRate)
        distance = try container.decode(Double.self, forKey: .distance)
        strokeDistance = try container.decode(Double.self, forKey: .strokeDistance)
        timePercentage = try container.decode(Double.self, forKey: .timePercentage)
        phaseType = try container.decode(PhaseType.self, forKey: .phaseType)
        allowsSubstitution = try container.decode(Bool.self, forKey: .allowsSubstitution)
        substitutionTiming = try container.decodeIfPresent(Double.self, forKey: .substitutionTiming)
        super.init()
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(duration, forKey: .duration)
        try container.encode(strokeRate, forKey: .strokeRate)
        try container.encode(distance, forKey: .distance)
        try container.encode(strokeDistance, forKey: .strokeDistance)
        try container.encode(timePercentage, forKey: .timePercentage)
        try container.encode(phaseType, forKey: .phaseType)
        try container.encode(allowsSubstitution, forKey: .allowsSubstitution)
        try container.encodeIfPresent(substitutionTiming, forKey: .substitutionTiming)
    }
}

// MARK: - Phase Type Enum

enum PhaseType: String, Codable, CaseIterable {
    case start = "start"
    case middle = "middle"
    case sprint = "sprint"
    case recovery = "recovery"
    
    var displayName: String {
        switch self {
        case .start:
            return "Start Phase"
        case .middle:
            return "Middle Phase"
        case .sprint:
            return "Sprint Phase"
        case .recovery:
            return "Recovery Phase"
        }
    }
    
    var color: UIColor {
        switch self {
        case .start:
            return UIColor.dragonAccent
        case .middle:
            return UIColor.dragonAccentSecondary
        case .sprint:
            return UIColor.dragonError
        case .recovery:
            return UIColor.dragonSuccess
        }
    }
    
    var icon: String {
        switch self {
        case .start:
            return "bolt.fill"
        case .middle:
            return "arrow.right"
        case .sprint:
            return "flame.fill"
        case .recovery:
            return "leaf.fill"
        }
    }
}

// MARK: - Equatable

extension RhythmPhase {
    override func isEqual(_ object: Any?) -> Bool {
        guard let other = object as? RhythmPhase else { return false }
        return id == other.id
    }
    
    override var hash: Int {
        return id.hashValue
    }
}
