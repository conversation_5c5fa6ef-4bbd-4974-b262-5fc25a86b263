//
//  TeamTimerView.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

protocol TeamTimerViewDelegate: AnyObject {
    func teamTimerView(_ teamTimerView: TeamTimerView, didFinishTeam team: Team)
}

class TeamTimerView: UIView {
    
    // MARK: - UI Components
    
    private let containerView = UIView()
    private let rankLabel = UILabel()
    private let teamNameLabel = UILabel()
    private let timeLabel = UILabel()
    private let finishButton = UIButton(type: .system)
    private let statusIconImageView = UIImageView()
    
    // MARK: - Properties
    
    let team: Team
    weak var delegate: TeamTimerViewDelegate?
    
    // MARK: - Initialization
    
    init(team: Team) {
        self.team = team
        super.init(frame: .zero)
        setupUI()
        setupConstraints()
        updateUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // Container view
        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        containerView.isUserInteractionEnabled = true // 确保容器视图允许交互
        addSubview(containerView)
        
        // Rank label
        rankLabel.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        rankLabel.textAlignment = .center
        rankLabel.layer.cornerRadius = 16
        rankLabel.layer.masksToBounds = true
        rankLabel.backgroundColor = UIColor.dragonSeparator
        containerView.addSubview(rankLabel)
        
        // Team name label
        teamNameLabel.text = team.name
        teamNameLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        teamNameLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(teamNameLabel)
        
        // Time label
        timeLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 16, weight: .medium)
        timeLabel.textColor = UIColor.dragonTextSecondary
        timeLabel.textAlignment = .right
        containerView.addSubview(timeLabel)
        
        // Status icon
        statusIconImageView.contentMode = .scaleAspectFit
        statusIconImageView.tintColor = UIColor.dragonTextTertiary
        containerView.addSubview(statusIconImageView)
        
        // Finish button
        finishButton.setTitle("Finish", for: .normal)
        finishButton.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        finishButton.setTitleColor(.white, for: .normal)
        finishButton.backgroundColor = UIColor.dragonSuccess
        finishButton.layer.cornerRadius = 6
        finishButton.isUserInteractionEnabled = true
        finishButton.addTarget(self, action: #selector(finishPressed), for: .touchUpInside)
        
        // 将按钮添加到最顶层，确保没有被其他视图覆盖
        containerView.addSubview(finishButton)
        containerView.bringSubviewToFront(finishButton)
        
        // 确保整个视图可以接收触摸事件
        self.isUserInteractionEnabled = true
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(80)
        }
        
        rankLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        teamNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(rankLabel.snp.trailing).offset(16)
            make.top.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(statusIconImageView.snp.leading).offset(-8)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.leading.equalTo(rankLabel.snp.trailing).offset(16)
            make.bottom.equalToSuperview().offset(-16)
            make.trailing.lessThanOrEqualTo(finishButton.snp.leading).offset(-12)
        }
        
        finishButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(70)
            make.height.equalTo(32)
        }

        statusIconImageView.snp.makeConstraints { make in
            make.trailing.equalTo(finishButton.snp.leading).offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(18)
        }
    }
    
    // MARK: - Actions
    
    @objc private func finishPressed() {
        print("Finish button pressed for team: \(team.name)")
        
        // 添加视觉反馈以确认按钮被点击
        UIView.animate(withDuration: 0.1, animations: {
            self.finishButton.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.finishButton.transform = .identity
            }
        }
        
        delegate?.teamTimerView(self, didFinishTeam: team)
    }
    
    // MARK: - Public Methods
    
    func updateUI() {
        // Update rank
        if let rank = team.rank {
            rankLabel.text = "\(rank)"
            rankLabel.isHidden = false
            
            // Set rank colors
            switch rank {
            case 1:
                rankLabel.backgroundColor = UIColor.dragonRankFirst
                rankLabel.textColor = .black
            case 2:
                rankLabel.backgroundColor = UIColor.dragonRankSecond
                rankLabel.textColor = .black
            case 3:
                rankLabel.backgroundColor = UIColor.dragonRankThird
                rankLabel.textColor = .white
            default:
                rankLabel.backgroundColor = UIColor.dragonSeparator
                rankLabel.textColor = UIColor.dragonTextPrimary
            }
        } else {
            rankLabel.isHidden = true
        }
        
        // Update time and status
        if team.isFinished {
            timeLabel.text = team.formattedFinishTime()
            timeLabel.textColor = UIColor.dragonSuccess
            
            statusIconImageView.image = UIImage(systemName: "checkmark.circle.fill")
            statusIconImageView.tintColor = UIColor.dragonSuccess
            statusIconImageView.isHidden = false
            
            finishButton.isHidden = true
            
            // Update container appearance
            containerView.layer.borderColor = UIColor.dragonSuccess.cgColor
            containerView.backgroundColor = UIColor.dragonSuccess.withAlphaComponent(0.1)
            
        } else {
            timeLabel.text = "Waiting..."
            timeLabel.textColor = UIColor.dragonTextTertiary
            
            statusIconImageView.image = UIImage(systemName: "clock")
            statusIconImageView.tintColor = UIColor.dragonTimerWaiting
            statusIconImageView.isHidden = false
            
            finishButton.isHidden = false
            // 确保按钮在最前面
            containerView.bringSubviewToFront(finishButton)

            // Update container appearance
            containerView.layer.borderColor = UIColor.dragonBorder.cgColor
            containerView.backgroundColor = UIColor.dragonCardBackground
        }
        
        // Animate changes
        UIView.transition(with: self, duration: 0.3, options: .transitionCrossDissolve, animations: {
            self.layoutIfNeeded()
        })
    }
    
    /// Animate team finish
    func animateFinish() {
        // Celebration animation
        UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: [], animations: {
            self.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        }) { _ in
            UIView.animate(withDuration: 0.3) {
                self.transform = .identity
            }
        }
        
        // Flash effect
        let flashView = UIView()
        flashView.backgroundColor = UIColor.dragonSuccess.withAlphaComponent(0.3)
        flashView.layer.cornerRadius = 12
        containerView.addSubview(flashView)
        flashView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        UIView.animate(withDuration: 0.6, animations: {
            flashView.alpha = 0
        }) { _ in
            flashView.removeFromSuperview()
        }
    }
}
