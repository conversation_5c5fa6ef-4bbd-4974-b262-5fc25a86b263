//
//  RaceRecord.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Represents a complete dragon boat race record
class RaceRecord: NSObject, Codable {
    
    // MARK: - Properties
    
    /// Unique identifier for the race
    let id: UUID
    
    /// Name of the race (optional, can be auto-generated)
    var name: String
    
    /// Date and time when the race was created
    let createdAt: Date
    
    /// Date and time when the race started (nil if not started)
    var startTime: Date?
    
    /// Date and time when the race ended (nil if not finished)
    var endTime: Date?
    
    /// List of teams participating in the race
    var teams: [Team]
    
    /// Current status of the race
    var status: RaceStatus
    
    /// Total duration of the race in seconds (from start to end)
    var raceDuration: TimeInterval? {
        guard let startTime = startTime, let endTime = endTime else {
            return nil
        }
        return endTime.timeIntervalSince(startTime)
    }
    
    /// Whether all teams have finished
    var allTeamsFinished: Bool {
        return teams.allSatisfy { $0.isFinished }
    }
    
    /// Number of teams that have finished
    var finishedTeamsCount: Int {
        return teams.filter { $0.isFinished }.count
    }
    
    // MARK: - Initialization
    
    /// Initialize a new race record
    /// - Parameters:
    ///   - name: Race name (optional, will auto-generate if empty)
    ///   - teams: List of participating teams
    init(name: String? = nil, teams: [Team] = []) {
        self.id = UUID()
        self.name = name ?? "Race \(TimeFormatter.currentTimestamp())"
        self.createdAt = Date()
        self.startTime = nil
        self.endTime = nil
        self.teams = teams
        self.status = .created
        super.init()
    }
    
    // MARK: - Race Management
    
    /// Start the race
    func startRace() {
        guard status == .created else { return }
        startTime = Date()
        status = .inProgress
        
        // Reset all teams
        teams.forEach { $0.reset() }
    }
    
    /// Finish a team at the current time
    /// - Parameter team: Team to finish
    /// - Returns: True if successful, false if team not found or race not in progress
    @discardableResult
    func finishTeam(_ team: Team) -> Bool {
        guard status == .inProgress,
              let startTime = startTime,
              let teamIndex = teams.firstIndex(of: team),
              !teams[teamIndex].isFinished else {
            return false
        }
        
        let finishTime = Date().timeIntervalSince(startTime)
        teams[teamIndex].finish(at: finishTime)
        
        // Update rankings
        updateRankings()
        
        return true
    }
    
    /// End the race manually
    func endRace() {
        guard status == .inProgress else { return }
        endTime = Date()
        status = .completed
        updateRankings()
    }
    
    /// Check if race should auto-end (all teams finished)
    func checkAutoEnd() {
        guard status == .inProgress, allTeamsFinished else { return }
        endRace()
    }
    
    /// Add a team to the race
    /// - Parameter team: Team to add
    /// - Returns: True if successful, false if race already started or team already exists
    @discardableResult
    func addTeam(_ team: Team) -> Bool {
        guard status == .created, !teams.contains(team) else {
            return false
        }
        teams.append(team)
        return true
    }
    
    /// Remove a team from the race
    /// - Parameter team: Team to remove
    /// - Returns: True if successful, false if race already started or team not found
    @discardableResult
    func removeTeam(_ team: Team) -> Bool {
        guard status == .created,
              let index = teams.firstIndex(of: team) else {
            return false
        }
        teams.remove(at: index)
        return true
    }
    
    // MARK: - Private Methods
    
    /// Update team rankings based on finish times
    private func updateRankings() {
        let finishedTeams = teams.filter { $0.isFinished }
        let sortedTeams = finishedTeams.sorted()
        
        for (index, team) in sortedTeams.enumerated() {
            team.rank = index + 1
        }
    }
    
    // MARK: - Computed Properties
    
    /// Get teams sorted by rank (finished teams first, then by name)
    var rankedTeams: [Team] {
        return teams.sorted()
    }
    
    /// Get the winning team (first place)
    var winner: Team? {
        return rankedTeams.first { $0.isFinished }
    }
    
    /// Get formatted race duration string
    var formattedDuration: String {
        guard let duration = raceDuration else {
            return "Not completed"
        }
        return TimeFormatter.formatRaceDuration(duration)
    }
    
    // MARK: - Codable
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case createdAt
        case startTime
        case endTime
        case teams
        case status
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        startTime = try container.decodeIfPresent(Date.self, forKey: .startTime)
        endTime = try container.decodeIfPresent(Date.self, forKey: .endTime)
        teams = try container.decode([Team].self, forKey: .teams)
        status = try container.decode(RaceStatus.self, forKey: .status)
        super.init()
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encodeIfPresent(startTime, forKey: .startTime)
        try container.encodeIfPresent(endTime, forKey: .endTime)
        try container.encode(teams, forKey: .teams)
        try container.encode(status, forKey: .status)
    }
}

// MARK: - Race Status Enum

enum RaceStatus: String, Codable, CaseIterable {
    case created = "created"
    case inProgress = "in_progress"
    case completed = "completed"
    
    var displayName: String {
        switch self {
        case .created:
            return "Created"
        case .inProgress:
            return "In Progress"
        case .completed:
            return "Completed"
        }
    }
}
