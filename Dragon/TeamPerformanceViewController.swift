//
//  TeamPerformanceViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class TeamPerformanceViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // Team Selection Section
    private let teamSelectionCard = UIView()
    private let teamSelectionLabel = UILabel()
    private let teamPickerView = UIPickerView()
    
    // Strategy Selection Section
    private let strategySelectionCard = UIView()
    private let strategySelectionLabel = UILabel()
    private let strategyPickerView = UIPickerView()
    
    // Analysis Results Section
    private let resultsCard = UIView()
    private let resultsLabel = UILabel()
    private let predictionLabel = UILabel()
    private let confidenceLabel = UILabel()
    private let comparisonLabel = UILabel()
    private let recommendationsLabel = UILabel()
    
    private let analyzeButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private let raceManager = RaceManager.shared
    private let strategyManager = StrategyManager.shared
    private var availableTeams: [Team] = []
    private var availableStrategies: [PaceStrategy] = []
    private var selectedTeam: Team?
    private var selectedStrategy: PaceStrategy?
    private var currentPrediction: PerformancePrediction?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadData()
        updateAnalysis()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Performance Analysis"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Team Performance Analysis"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Predict race outcomes based on historical data and strategy selection"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupTeamSelectionSection()
        setupStrategySelectionSection()
        setupResultsSection()
        setupAnalyzeButton()
    }
    
    private func setupTeamSelectionSection() {
        teamSelectionCard.backgroundColor = UIColor.dragonCardBackground
        teamSelectionCard.layer.cornerRadius = 12
        teamSelectionCard.layer.borderWidth = 1
        teamSelectionCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(teamSelectionCard)
        
        teamSelectionLabel.text = "Select Team"
        teamSelectionLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        teamSelectionLabel.textColor = UIColor.dragonTextPrimary
        teamSelectionCard.addSubview(teamSelectionLabel)
        
        teamPickerView.delegate = self
        teamPickerView.dataSource = self
        teamSelectionCard.addSubview(teamPickerView)
    }
    
    private func setupStrategySelectionSection() {
        strategySelectionCard.backgroundColor = UIColor.dragonCardBackground
        strategySelectionCard.layer.cornerRadius = 12
        strategySelectionCard.layer.borderWidth = 1
        strategySelectionCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(strategySelectionCard)
        
        strategySelectionLabel.text = "Select Strategy"
        strategySelectionLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        strategySelectionLabel.textColor = UIColor.dragonTextPrimary
        strategySelectionCard.addSubview(strategySelectionLabel)
        
        strategyPickerView.delegate = self
        strategyPickerView.dataSource = self
        strategySelectionCard.addSubview(strategyPickerView)
    }
    
    private func setupResultsSection() {
        resultsCard.backgroundColor = UIColor.dragonCardBackground
        resultsCard.layer.cornerRadius = 12
        resultsCard.layer.borderWidth = 1
        resultsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(resultsCard)
        
        resultsLabel.text = "Performance Prediction"
        resultsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        resultsLabel.textColor = UIColor.dragonTextPrimary
        resultsCard.addSubview(resultsLabel)
        
        predictionLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        predictionLabel.textColor = UIColor.dragonTextSecondary
        predictionLabel.numberOfLines = 0
        resultsCard.addSubview(predictionLabel)
        
        confidenceLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        confidenceLabel.textColor = UIColor.dragonTextTertiary
        confidenceLabel.numberOfLines = 0
        resultsCard.addSubview(confidenceLabel)
        
        comparisonLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        comparisonLabel.textColor = UIColor.dragonTextSecondary
        comparisonLabel.numberOfLines = 0
        resultsCard.addSubview(comparisonLabel)
        
        recommendationsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        recommendationsLabel.textColor = UIColor.dragonAccentSecondary
        recommendationsLabel.numberOfLines = 0
        resultsCard.addSubview(recommendationsLabel)
    }
    
    private func setupAnalyzeButton() {
        analyzeButton.setTitle("Analyze Performance", for: .normal)
        analyzeButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        analyzeButton.setTitleColor(.white, for: .normal)
        analyzeButton.backgroundColor = UIColor.dragonAccent
        analyzeButton.layer.cornerRadius = 12
        analyzeButton.addTarget(self, action: #selector(analyzePressed), for: .touchUpInside)
        contentView.addSubview(analyzeButton)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        // Team Selection Card
        teamSelectionCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        teamSelectionLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        teamPickerView.snp.makeConstraints { make in
            make.top.equalTo(teamSelectionLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(120)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        // Strategy Selection Card
        strategySelectionCard.snp.makeConstraints { make in
            make.top.equalTo(teamSelectionCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        strategySelectionLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        strategyPickerView.snp.makeConstraints { make in
            make.top.equalTo(strategySelectionLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(120)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        // Results Card
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(strategySelectionCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        resultsLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        predictionLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        confidenceLabel.snp.makeConstraints { make in
            make.top.equalTo(predictionLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        comparisonLabel.snp.makeConstraints { make in
            make.top.equalTo(confidenceLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        recommendationsLabel.snp.makeConstraints { make in
            make.top.equalTo(comparisonLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        // Analyze Button
        analyzeButton.snp.makeConstraints { make in
            make.top.equalTo(resultsCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    // MARK: - Data Loading
    
    private func loadData() {
        // Load teams from completed races
        let completedRaces = raceManager.completedRaces
        var teamSet = Set<String>()
        
        for race in completedRaces {
            for team in race.teams where team.isFinished {
                teamSet.insert(team.name)
            }
        }
        
        availableTeams = Array(teamSet).sorted().map { Team(name: $0) }
        
        // Load strategies
        availableStrategies = strategyManager.strategies
        
        // Set initial selections
        if !availableTeams.isEmpty {
            selectedTeam = availableTeams[0]
        }
        
        if !availableStrategies.isEmpty {
            selectedStrategy = availableStrategies[0]
        }
        
        teamPickerView.reloadAllComponents()
        strategyPickerView.reloadAllComponents()
    }
    
    // MARK: - Actions
    
    @objc private func analyzePressed() {
        updateAnalysis()
    }
    
    // MARK: - Analysis
    
    private func updateAnalysis() {
        guard let team = selectedTeam, let strategy = selectedStrategy else {
            clearResults()
            return
        }
        
        currentPrediction = strategyManager.predictPerformance(for: team, using: strategy)
        updateResultsDisplay()
    }
    
    private func updateResultsDisplay() {
        guard let prediction = currentPrediction else {
            clearResults()
            return
        }
        
        predictionLabel.text = "Predicted Time: \(prediction.formattedPredictedTime)"
        
        confidenceLabel.text = "Confidence: \(prediction.confidenceDescription) (based on \(prediction.basedOnRaces) races)"
        
        let timeDiff = prediction.timeDifference
        if abs(timeDiff) < 1.0 {
            comparisonLabel.text = "Performance matches strategy target closely"
            comparisonLabel.textColor = UIColor.dragonSuccess
        } else if timeDiff > 0 {
            comparisonLabel.text = "Predicted \(TimeFormatter.formatTime(abs(timeDiff))) slower than target"
            comparisonLabel.textColor = UIColor.dragonWarning
        } else {
            comparisonLabel.text = "Predicted \(TimeFormatter.formatTime(abs(timeDiff))) faster than target"
            comparisonLabel.textColor = UIColor.dragonSuccess
        }
        
        // Generate recommendations
        var recommendations: [String] = []
        
        if prediction.confidence < 0.5 {
            recommendations.append("• More race data needed for accurate prediction")
        }
        
        if prediction.isSlowerThanTarget {
            recommendations.append("• Consider adjusting strategy for better performance")
            recommendations.append("• Focus on improving stroke efficiency")
        } else {
            recommendations.append("• Strategy appears well-suited for this team")
        }
        
        if prediction.basedOnRaces < 3 {
            recommendations.append("• Participate in more races to improve prediction accuracy")
        }
        
        recommendationsLabel.text = recommendations.isEmpty ? "No specific recommendations" : "Recommendations:\n" + recommendations.joined(separator: "\n")
    }
    
    private func clearResults() {
        predictionLabel.text = "Select team and strategy to see prediction"
        confidenceLabel.text = ""
        comparisonLabel.text = ""
        recommendationsLabel.text = ""
    }
}

// MARK: - UIPickerViewDataSource

extension TeamPerformanceViewController: UIPickerViewDataSource {
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 1
    }

    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        if pickerView == teamPickerView {
            return availableTeams.isEmpty ? 1 : availableTeams.count
        } else {
            return availableStrategies.isEmpty ? 1 : availableStrategies.count
        }
    }
}

// MARK: - UIPickerViewDelegate

extension TeamPerformanceViewController: UIPickerViewDelegate {
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        if pickerView == teamPickerView {
            if availableTeams.isEmpty {
                return "No teams available"
            }
            return availableTeams[row].name
        } else {
            if availableStrategies.isEmpty {
                return "No strategies available"
            }
            return availableStrategies[row].name
        }
    }

    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        if pickerView == teamPickerView {
            if !availableTeams.isEmpty {
                selectedTeam = availableTeams[row]
            }
        } else {
            if !availableStrategies.isEmpty {
                selectedStrategy = availableStrategies[row]
            }
        }

        updateAnalysis()
    }

    func pickerView(_ pickerView: UIPickerView, attributedTitleForRow row: Int, forComponent component: Int) -> NSAttributedString? {
        let title: String
        let color: UIColor

        if pickerView == teamPickerView {
            if availableTeams.isEmpty {
                title = "No teams available"
                color = UIColor.dragonTextTertiary
            } else {
                title = availableTeams[row].name
                color = UIColor.dragonTextPrimary
            }
        } else {
            if availableStrategies.isEmpty {
                title = "No strategies available"
                color = UIColor.dragonTextTertiary
            } else {
                title = availableStrategies[row].name
                color = UIColor.dragonTextPrimary
            }
        }

        return NSAttributedString(string: title, attributes: [.foregroundColor: color])
    }
}
