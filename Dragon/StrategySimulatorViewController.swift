//
//  StrategySimulatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategySimulatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // 功能卡片
    private let strokeRateSimulatorCard = UIView()
    private let strategyTemplatesCard = UIView()
    private let strokeCalculatorCard = UIView()
    private let performanceSimulatorCard = UIView()
    
    // 当前策略显示
    private let currentStrategyCard = UIView()
    private let currentStrategyLabel = UILabel()
    private let currentStrategyNameLabel = UILabel()
    private let currentStrategyDetailsLabel = UILabel()
    private let editStrategyButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupStrategyManager()
        updateCurrentStrategyDisplay()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateCurrentStrategyDisplay()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Strategy Simulator"

        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)

        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)

        // Setup title
        titleLabel.text = "Dragon Boat Strategy Simulator"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)

        subtitleLabel.text = "Create race strategies, optimize stroke rates, and predict performance"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)

        setupCurrentStrategyCard()
        setupFeatureCards()
    }
    
    private func setupCurrentStrategyCard() {
        currentStrategyCard.backgroundColor = UIColor.dragonCardBackground
        currentStrategyCard.layer.cornerRadius = 12
        currentStrategyCard.layer.borderWidth = 1
        currentStrategyCard.layer.borderColor = UIColor.dragonAccentSecondary.cgColor
        currentStrategyCard.isHidden = true
        contentView.addSubview(currentStrategyCard)
        
        currentStrategyLabel.text = "Current Strategy"
        currentStrategyLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        currentStrategyLabel.textColor = UIColor.dragonTextPrimary
        currentStrategyCard.addSubview(currentStrategyLabel)

        currentStrategyNameLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        currentStrategyNameLabel.textColor = UIColor.dragonAccentSecondary
        currentStrategyCard.addSubview(currentStrategyNameLabel)

        currentStrategyDetailsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        currentStrategyDetailsLabel.textColor = UIColor.dragonTextSecondary
        currentStrategyDetailsLabel.numberOfLines = 0
        currentStrategyCard.addSubview(currentStrategyDetailsLabel)

        editStrategyButton.setTitle("Edit", for: .normal)
        editStrategyButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        editStrategyButton.setTitleColor(.white, for: .normal)
        editStrategyButton.backgroundColor = UIColor.dragonAccentSecondary
        editStrategyButton.layer.cornerRadius = 8
        editStrategyButton.addTarget(self, action: #selector(editCurrentStrategyPressed), for: .touchUpInside)
        currentStrategyCard.addSubview(editStrategyButton)
    }
    
    private func setupFeatureCards() {
        // Stroke Rate Simulator
        setupFeatureCard(
            card: strokeRateSimulatorCard,
            title: "Stroke Rate Simulator",
            subtitle: "Phase-based rhythm strategy",
            description: "Input race distance and target time, divide rhythm by start/middle/sprint phases, auto-calculate stroke rates and substitution timing",
            icon: "speedometer",
            color: UIColor.systemBlue,
            action: #selector(openStrokeRateSimulatorPressed)
        )

        // Strategy Templates
        setupFeatureCard(
            card: strategyTemplatesCard,
            title: "Strategy Templates",
            subtitle: "Tactical rhythm templates",
            description: "Choose from even pace, burst, sprint finish and other tactical templates. Customize and save as your own strategy",
            icon: "doc.text.fill",
            color: UIColor.systemGreen,
            action: #selector(openStrategyTemplatesPressed)
        )

        // Stroke Calculator
        setupFeatureCard(
            card: strokeCalculatorCard,
            title: "Stroke Calculator",
            subtitle: "Precise stroke data calculation",
            description: "Input stroke distance to auto-calculate total strokes needed, average stroke rate and detailed phase data",
            icon: "function",
            color: UIColor.systemOrange,
            action: #selector(openStrokeCalculatorPressed)
        )

        // Performance Simulator
        setupFeatureCard(
            card: performanceSimulatorCard,
            title: "Performance Simulator",
            subtitle: "Predict race performance",
            description: "Import historical team data to simulate expected results with different rhythm strategies and get scientific recommendations",
            icon: "chart.line.uptrend.xyaxis",
            color: UIColor.systemPurple,
            action: #selector(openPerformanceSimulatorPressed)
        )
    }
    
    private func setupFeatureCard(card: UIView, title: String, subtitle: String, description: String, icon: String, color: UIColor, action: Selector) {
        card.backgroundColor = UIColor.dragonCardBackground
        card.layer.cornerRadius = 12
        card.layer.borderWidth = 1
        card.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(card)
        
        // 图标
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit
        card.addSubview(iconImageView)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        card.addSubview(titleLabel)
        
        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        subtitleLabel.textColor = color
        card.addSubview(subtitleLabel)
        
        // 描述
        let descriptionLabel = UILabel()
        descriptionLabel.text = description
        descriptionLabel.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        descriptionLabel.textColor = UIColor.dragonTextSecondary
        descriptionLabel.numberOfLines = 0
        card.addSubview(descriptionLabel)
        
        // 箭头
        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        card.addSubview(chevronImageView)
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        card.addGestureRecognizer(tapGesture)
        card.isUserInteractionEnabled = true
        
        // 布局
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalTo(subtitleLabel.snp.bottom).offset(8)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        currentStrategyCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(100)
        }
        
        currentStrategyLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        currentStrategyNameLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(editStrategyButton.snp.leading).offset(-16)
        }
        
        currentStrategyDetailsLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyNameLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(editStrategyButton.snp.leading).offset(-16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }
        
        editStrategyButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(60)
            make.height.equalTo(36)
        }
        
        // 功能卡片布局
        let topAnchor = currentStrategyCard.snp.bottom
        
        strokeRateSimulatorCard.snp.makeConstraints { make in
            make.top.equalTo(topAnchor).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
        
        strategyTemplatesCard.snp.makeConstraints { make in
            make.top.equalTo(strokeRateSimulatorCard.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
        
        strokeCalculatorCard.snp.makeConstraints { make in
            make.top.equalTo(strategyTemplatesCard.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
        
        performanceSimulatorCard.snp.makeConstraints { make in
            make.top.equalTo(strokeCalculatorCard.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupStrategyManager() {
        strategyManager.delegate = self
    }

    // MARK: - Actions

    @objc private func editCurrentStrategyPressed() {
        guard let strategy = strategyManager.currentStrategy else { return }
        let editorVC = StrategyEditorViewController(strategy: strategy)
        let navController = UINavigationController(rootViewController: editorVC)
        present(navController, animated: true)
    }

    @objc private func openStrokeRateSimulatorPressed() {
        let simulatorVC = StrokeRateSimulatorViewController()
        navigationController?.pushViewController(simulatorVC, animated: true)
    }

    @objc private func openStrategyTemplatesPressed() {
        let templatesVC = StrategyTemplatesViewController()
        navigationController?.pushViewController(templatesVC, animated: true)
    }

    @objc private func openStrokeCalculatorPressed() {
        let calculatorVC = StrokeCalculatorViewController()
        navigationController?.pushViewController(calculatorVC, animated: true)
    }

    @objc private func openPerformanceSimulatorPressed() {
        let performanceVC = PerformanceSimulatorViewController()
        navigationController?.pushViewController(performanceVC, animated: true)
    }

    // MARK: - UI Updates

    private func updateCurrentStrategyDisplay() {
        if let strategy = strategyManager.currentStrategy {
            currentStrategyCard.isHidden = false
            currentStrategyNameLabel.text = strategy.name

            let details = "\(Int(strategy.raceDistance))m • \(TimeFormatter.formatTime(strategy.targetTime)) • \(strategy.phases.count) phases"
            currentStrategyDetailsLabel.text = details

            // 更新约束
            updateConstraintsForCurrentStrategy(hidden: false)
        } else {
            currentStrategyCard.isHidden = true
            updateConstraintsForCurrentStrategy(hidden: true)
        }
    }

    private func updateConstraintsForCurrentStrategy(hidden: Bool) {
        let topAnchor = hidden ? subtitleLabel.snp.bottom : currentStrategyCard.snp.bottom

        strokeRateSimulatorCard.snp.remakeConstraints { make in
            make.top.equalTo(topAnchor).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
    }
}

// MARK: - StrategyManagerDelegate

extension StrategySimulatorViewController: StrategyManagerDelegate {
    func strategyManager(_ manager: StrategyManager, didUpdateStrategy strategy: RaceStrategy) {
        DispatchQueue.main.async {
            self.updateCurrentStrategyDisplay()
        }
    }

    func strategyManager(_ manager: StrategyManager, didDeleteStrategy strategy: RaceStrategy) {
        DispatchQueue.main.async {
            self.updateCurrentStrategyDisplay()
        }
    }

    func strategyManager(_ manager: StrategyManager, didSetCurrentStrategy strategy: RaceStrategy?) {
        DispatchQueue.main.async {
            self.updateCurrentStrategyDisplay()
        }
    }
}
