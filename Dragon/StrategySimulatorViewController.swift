//
//  StrategySimulatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategySimulatorViewController: UIViewController {

    // MARK: - UI Components

    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()

    // Basic Calculator Card
    private let calculatorCard = UIView()
    private let calculatorTitleLabel = UILabel()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    private let strokeDistanceTextField = UITextField()
    private let calculateButton = UIButton(type: .system)

    // Results Card
    private let resultsCard = UIView()
    private let resultsTitleLabel = UILabel()
    private let totalStrokesLabel = UILabel()
    private let strokeRateLabel = UILabel()
    private let averageSpeedLabel = UILabel()

    // MARK: - Lifecycle

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }

    // MARK: - Setup

    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Strategy Simulator"

        // Setup title
        titleLabel.text = "Pace & Rhythm Calculator"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        view.addSubview(titleLabel)

        subtitleLabel.text = "Calculate optimal stroke rates and race strategy"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        view.addSubview(subtitleLabel)

        setupCalculatorCard()
        setupResultsCard()
    }

    private func setupCalculatorCard() {
        calculatorCard.backgroundColor = UIColor.dragonCardBackground
        calculatorCard.layer.cornerRadius = 12
        calculatorCard.layer.borderWidth = 1
        calculatorCard.layer.borderColor = UIColor.dragonBorder.cgColor
        view.addSubview(calculatorCard)

        calculatorTitleLabel.text = "Race Parameters"
        calculatorTitleLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        calculatorTitleLabel.textColor = UIColor.dragonTextPrimary
        calculatorCard.addSubview(calculatorTitleLabel)

        // Distance field
        distanceTextField.placeholder = "Race Distance (meters)"
        distanceTextField.text = "500"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        calculatorCard.addSubview(distanceTextField)

        // Target time field
        targetTimeTextField.placeholder = "Target Time (MM:SS)"
        targetTimeTextField.text = "2:00"
        setupTextField(targetTimeTextField)
        calculatorCard.addSubview(targetTimeTextField)

        // Stroke distance field
        strokeDistanceTextField.placeholder = "Stroke Distance (meters)"
        strokeDistanceTextField.text = "2.5"
        strokeDistanceTextField.keyboardType = .decimalPad
        setupTextField(strokeDistanceTextField)
        calculatorCard.addSubview(strokeDistanceTextField)

        // Calculate button
        calculateButton.setTitle("Calculate Strategy", for: .normal)
        calculateButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        calculateButton.setTitleColor(.white, for: .normal)
        calculateButton.backgroundColor = UIColor.dragonAccent
        calculateButton.layer.cornerRadius = 12
        calculateButton.addTarget(self, action: #selector(calculatePressed), for: .touchUpInside)
        calculatorCard.addSubview(calculateButton)
    }

    private func setupResultsCard() {
        resultsCard.backgroundColor = UIColor.dragonCardBackground
        resultsCard.layer.cornerRadius = 12
        resultsCard.layer.borderWidth = 1
        resultsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        view.addSubview(resultsCard)

        resultsTitleLabel.text = "Strategy Results"
        resultsTitleLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        resultsTitleLabel.textColor = UIColor.dragonTextPrimary
        resultsCard.addSubview(resultsTitleLabel)

        totalStrokesLabel.text = "Total Strokes: --"
        totalStrokesLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        totalStrokesLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(totalStrokesLabel)

        strokeRateLabel.text = "Required Stroke Rate: -- SPM"
        strokeRateLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        strokeRateLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(strokeRateLabel)

        averageSpeedLabel.text = "Average Speed: -- km/h"
        averageSpeedLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        averageSpeedLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(averageSpeedLabel)
    }

    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonSecondaryBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always

        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }

    private func setupConstraints() {
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Calculator Card
        calculatorCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        calculatorTitleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(calculatorTitleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        strokeDistanceTextField.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        calculateButton.snp.makeConstraints { make in
            make.top.equalTo(strokeDistanceTextField.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Results Card
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(calculatorCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        resultsTitleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        totalStrokesLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsTitleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        strokeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(totalStrokesLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        averageSpeedLabel.snp.makeConstraints { make in
            make.top.equalTo(strokeRateLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }

    // MARK: - Actions

    @objc private func calculatePressed() {
        print("Calculate button pressed!")

        guard let distanceText = distanceTextField.text,
              let distance = Double(distanceText),
              distance > 0 else {
            showAlert(title: "Invalid Distance", message: "Please enter a valid race distance.")
            return
        }

        guard let strokeDistanceText = strokeDistanceTextField.text,
              let strokeDistance = Double(strokeDistanceText),
              strokeDistance > 0 else {
            showAlert(title: "Invalid Stroke Distance", message: "Please enter a valid stroke distance.")
            return
        }

        let targetTime = parseTime(targetTimeTextField.text ?? "2:00")

        // Calculate results
        let totalStrokes = Int(ceil(distance / strokeDistance))
        let strokeRate = (Double(totalStrokes) / targetTime) * 60 // SPM
        let averageSpeed = distance / targetTime // m/s
        let averageSpeedKmh = averageSpeed * 3.6 // km/h

        // Update UI
        totalStrokesLabel.text = "Total Strokes: \(totalStrokes)"
        strokeRateLabel.text = "Required Stroke Rate: \(Int(strokeRate)) SPM"
        averageSpeedLabel.text = "Average Speed: \(String(format: "%.1f", averageSpeedKmh)) km/h"

        // Change colors to indicate success
        totalStrokesLabel.textColor = UIColor.dragonAccentSecondary
        strokeRateLabel.textColor = UIColor.dragonAccentSecondary
        averageSpeedLabel.textColor = UIColor.dragonAccentSecondary
    }

    // MARK: - Helper Methods

    private func parseTime(_ timeString: String) -> TimeInterval {
        let components = timeString.components(separatedBy: ":")
        if components.count == 2,
           let minutes = Double(components[0]),
           let seconds = Double(components[1]) {
            return (minutes * 60) + seconds
        }
        return 120.0 // Default 2 minutes
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}