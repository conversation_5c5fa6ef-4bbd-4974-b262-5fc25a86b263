//
//  StrategySimulatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategySimulatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    private let quickStartCard = UIView()
    private let quickStartLabel = UILabel()
    private let newStrategyButton = UIButton(type: .system)
    private let templatesButton = UIButton(type: .system)
    
    private let currentStrategyCard = UIView()
    private let currentStrategyLabel = UILabel()
    private let currentStrategyNameLabel = UILabel()
    private let currentStrategyDetailsLabel = UILabel()
    private let editStrategyButton = UIButton(type: .system)
    
    private let featuresLabel = UILabel()
    private let featuresStackView = UIStackView()
    
    private let strategiesLabel = UILabel()
    private let strategiesTableView = UITableView()
    private let emptyStateLabel = UILabel()
    
    // MARK: - Properties
    
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupStrategyManager()
        updateUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateUI()
        strategiesTableView.reloadData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Strategy Simulator"
        
        // Navigation
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "plus"),
            style: .plain,
            target: self,
            action: #selector(createNewStrategyPressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Pace & Rhythm Simulator"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Optimize your race strategy with data-driven insights"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        // Setup quick start card
        setupQuickStartCard()
        
        // Setup current strategy card
        setupCurrentStrategyCard()
        
        // Setup features section
        setupFeaturesSection()
        
        // Setup strategies list
        setupStrategiesSection()
    }
    
    private func setupQuickStartCard() {
        quickStartCard.backgroundColor = UIColor.dragonCardBackground
        quickStartCard.layer.cornerRadius = 12
        quickStartCard.layer.borderWidth = 1
        quickStartCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(quickStartCard)
        
        quickStartLabel.text = "Quick Start"
        quickStartLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        quickStartLabel.textColor = UIColor.dragonTextPrimary
        quickStartCard.addSubview(quickStartLabel)
        
        newStrategyButton.setTitle("Create New Strategy", for: .normal)
        newStrategyButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        newStrategyButton.setTitleColor(.white, for: .normal)
        newStrategyButton.backgroundColor = UIColor.dragonAccent
        newStrategyButton.layer.cornerRadius = 8
        newStrategyButton.addTarget(self, action: #selector(createNewStrategyPressed), for: .touchUpInside)
        quickStartCard.addSubview(newStrategyButton)
        
        templatesButton.setTitle("Browse Templates", for: .normal)
        templatesButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        templatesButton.setTitleColor(UIColor.dragonAccent, for: .normal)
        templatesButton.backgroundColor = UIColor.dragonCardBackground
        templatesButton.layer.cornerRadius = 8
        templatesButton.layer.borderWidth = 1
        templatesButton.layer.borderColor = UIColor.dragonAccent.cgColor
        templatesButton.addTarget(self, action: #selector(browseTemplatesPressed), for: .touchUpInside)
        quickStartCard.addSubview(templatesButton)
    }
    
    private func setupCurrentStrategyCard() {
        currentStrategyCard.backgroundColor = UIColor.dragonCardBackground
        currentStrategyCard.layer.cornerRadius = 12
        currentStrategyCard.layer.borderWidth = 1
        currentStrategyCard.layer.borderColor = UIColor.dragonAccentSecondary.cgColor
        currentStrategyCard.isHidden = true
        contentView.addSubview(currentStrategyCard)
        
        currentStrategyLabel.text = "Current Strategy"
        currentStrategyLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        currentStrategyLabel.textColor = UIColor.dragonTextPrimary
        currentStrategyCard.addSubview(currentStrategyLabel)
        
        currentStrategyNameLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        currentStrategyNameLabel.textColor = UIColor.dragonAccentSecondary
        currentStrategyCard.addSubview(currentStrategyNameLabel)
        
        currentStrategyDetailsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        currentStrategyDetailsLabel.textColor = UIColor.dragonTextSecondary
        currentStrategyDetailsLabel.numberOfLines = 0
        currentStrategyCard.addSubview(currentStrategyDetailsLabel)
        
        editStrategyButton.setTitle("Edit", for: .normal)
        editStrategyButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        editStrategyButton.setTitleColor(.white, for: .normal)
        editStrategyButton.backgroundColor = UIColor.dragonAccentSecondary
        editStrategyButton.layer.cornerRadius = 8
        editStrategyButton.addTarget(self, action: #selector(editCurrentStrategyPressed), for: .touchUpInside)
        currentStrategyCard.addSubview(editStrategyButton)
    }
    
    private func setupFeaturesSection() {
        featuresLabel.text = "Features"
        featuresLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        featuresLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(featuresLabel)
        
        featuresStackView.axis = .vertical
        featuresStackView.spacing = 12
        featuresStackView.distribution = .fill
        contentView.addSubview(featuresStackView)
        
        // Add feature cards
        addFeatureCard(
            title: "Stroke Rate Calculator",
            description: "Calculate optimal stroke rates for different race phases",
            icon: "speedometer",
            action: #selector(openStrokeCalculatorPressed)
        )
        
        addFeatureCard(
            title: "Team Performance Analysis",
            description: "Predict race outcomes based on historical data",
            icon: "chart.line.uptrend.xyaxis",
            action: #selector(openPerformanceAnalysisPressed)
        )
        
        addFeatureCard(
            title: "Strategy Comparison",
            description: "Compare different strategies side by side",
            icon: "rectangle.split.2x1",
            action: #selector(openStrategyComparisonPressed)
        )
    }
    
    private func addFeatureCard(title: String, description: String, icon: String, action: Selector) {
        let card = UIView()
        card.backgroundColor = UIColor.dragonCardBackground
        card.layer.cornerRadius = 8
        card.layer.borderWidth = 1
        card.layer.borderColor = UIColor.dragonBorder.cgColor
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = UIColor.dragonAccent
        iconImageView.contentMode = .scaleAspectFit
        card.addSubview(iconImageView)
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        card.addSubview(titleLabel)
        
        let descriptionLabel = UILabel()
        descriptionLabel.text = description
        descriptionLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        descriptionLabel.textColor = UIColor.dragonTextSecondary
        descriptionLabel.numberOfLines = 0
        card.addSubview(descriptionLabel)
        
        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        card.addSubview(chevronImageView)
        
        // Add tap gesture
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        card.addGestureRecognizer(tapGesture)
        card.isUserInteractionEnabled = true
        
        // Layout
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().offset(12)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        card.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(60)
        }
        
        featuresStackView.addArrangedSubview(card)
    }
    
    private func setupStrategiesSection() {
        strategiesLabel.text = "My Strategies"
        strategiesLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        strategiesLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(strategiesLabel)
        
        strategiesTableView.backgroundColor = .clear
        strategiesTableView.separatorStyle = .none
        strategiesTableView.delegate = self
        strategiesTableView.dataSource = self
        strategiesTableView.register(StrategyCell.self, forCellReuseIdentifier: "StrategyCell")
        contentView.addSubview(strategiesTableView)
        
        emptyStateLabel.text = "No strategies yet.\nCreate your first strategy to get started!"
        emptyStateLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        emptyStateLabel.textColor = UIColor.dragonTextTertiary
        emptyStateLabel.textAlignment = .center
        emptyStateLabel.numberOfLines = 0
        emptyStateLabel.isHidden = true
        contentView.addSubview(emptyStateLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        quickStartCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        quickStartLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        newStrategyButton.snp.makeConstraints { make in
            make.top.equalTo(quickStartLabel.snp.bottom).offset(16)
            make.leading.equalToSuperview().offset(16)
            make.width.equalTo(160)
            make.height.equalTo(44)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        templatesButton.snp.makeConstraints { make in
            make.top.equalTo(newStrategyButton)
            make.leading.equalTo(newStrategyButton.snp.trailing).offset(12)
            make.trailing.lessThanOrEqualToSuperview().offset(-16)
            make.height.equalTo(44)
        }
        
        currentStrategyCard.snp.makeConstraints { make in
            make.top.equalTo(quickStartCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        currentStrategyLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        currentStrategyNameLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(editStrategyButton.snp.leading).offset(-16)
        }
        
        currentStrategyDetailsLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyNameLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(editStrategyButton.snp.leading).offset(-16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        editStrategyButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(60)
            make.height.equalTo(36)
        }
        
        featuresLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        featuresStackView.snp.makeConstraints { make in
            make.top.equalTo(featuresLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        strategiesLabel.snp.makeConstraints { make in
            make.top.equalTo(featuresStackView.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        strategiesTableView.snp.makeConstraints { make in
            make.top.equalTo(strategiesLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(300)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        emptyStateLabel.snp.makeConstraints { make in
            make.center.equalTo(strategiesTableView)
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
    
    private func setupStrategyManager() {
        strategyManager.delegate = self
    }
    
    // MARK: - Actions
    
    @objc private func createNewStrategyPressed() {
        let calculatorVC = PaceCalculatorViewController()
        let navController = UINavigationController(rootViewController: calculatorVC)
        present(navController, animated: true)
    }
    
    @objc private func browseTemplatesPressed() {
        let templatesVC = StrategyTemplatesViewController()
        navigationController?.pushViewController(templatesVC, animated: true)
    }
    
    @objc private func editCurrentStrategyPressed() {
        guard let strategy = strategyManager.currentStrategy else { return }
        let calculatorVC = PaceCalculatorViewController(strategy: strategy)
        let navController = UINavigationController(rootViewController: calculatorVC)
        present(navController, animated: true)
    }
    
    @objc private func openStrokeCalculatorPressed() {
        let calculatorVC = PaceCalculatorViewController()
        navigationController?.pushViewController(calculatorVC, animated: true)
    }
    
    @objc private func openPerformanceAnalysisPressed() {
        let performanceVC = TeamPerformanceViewController()
        navigationController?.pushViewController(performanceVC, animated: true)
    }
    
    @objc private func openStrategyComparisonPressed() {
        // TODO: Implement strategy comparison view
        let alert = UIAlertController(title: "Coming Soon", message: "Strategy comparison feature will be available in a future update.", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - UI Updates
    
    private func updateUI() {
        updateCurrentStrategyCard()
        updateStrategiesList()
    }
    
    private func updateCurrentStrategyCard() {
        if let strategy = strategyManager.currentStrategy {
            currentStrategyCard.isHidden = false
            currentStrategyNameLabel.text = strategy.name
            
            let details = "\(Int(strategy.raceDistance))m • \(TimeFormatter.formatTime(strategy.targetTime)) • \(strategy.phases.count) phases"
            currentStrategyDetailsLabel.text = details
        } else {
            currentStrategyCard.isHidden = true
        }
        
        // Update constraints
        let topConstraint = currentStrategyCard.isHidden ? quickStartCard.snp.bottom : currentStrategyCard.snp.bottom
        featuresLabel.snp.remakeConstraints { make in
            make.top.equalTo(topConstraint).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
    }
    
    private func updateStrategiesList() {
        let hasStrategies = !strategyManager.customStrategies.isEmpty
        emptyStateLabel.isHidden = hasStrategies
        strategiesTableView.isHidden = !hasStrategies
    }
}

// MARK: - StrategyManagerDelegate

extension StrategySimulatorViewController: StrategyManagerDelegate {
    func strategyManager(_ manager: StrategyManager, didUpdateStrategy strategy: PaceStrategy) {
        DispatchQueue.main.async {
            self.updateUI()
            self.strategiesTableView.reloadData()
        }
    }

    func strategyManager(_ manager: StrategyManager, didDeleteStrategy strategy: PaceStrategy) {
        DispatchQueue.main.async {
            self.updateUI()
            self.strategiesTableView.reloadData()
        }
    }
}

// MARK: - UITableViewDataSource

extension StrategySimulatorViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return strategyManager.customStrategies.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "StrategyCell", for: indexPath) as! StrategyCell
        let strategy = strategyManager.customStrategies[indexPath.row]
        cell.configure(with: strategy)
        return cell
    }
}

// MARK: - UITableViewDelegate

extension StrategySimulatorViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 80
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let strategy = strategyManager.customStrategies[indexPath.row]
        strategyManager.setCurrentStrategy(strategy)

        let calculatorVC = PaceCalculatorViewController(strategy: strategy)
        navigationController?.pushViewController(calculatorVC, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let strategy = strategyManager.customStrategies[indexPath.row]

            let alert = UIAlertController(title: "Delete Strategy",
                                        message: "Are you sure you want to delete '\(strategy.name)'?",
                                        preferredStyle: .alert)

            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                self.strategyManager.deleteStrategy(strategy)
            })

            present(alert, animated: true)
        }
    }
}

// MARK: - StrategyCell

class StrategyCell: UITableViewCell {

    private let containerView = UIView()
    private let nameLabel = UILabel()
    private let detailsLabel = UILabel()
    private let typeLabel = UILabel()
    private let chevronImageView = UIImageView()
    private let validationImageView = UIImageView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(containerView)

        nameLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        nameLabel.textColor = UIColor.dragonTextPrimary
        containerView.addSubview(nameLabel)

        detailsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        detailsLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(detailsLabel)

        typeLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        typeLabel.textColor = UIColor.dragonAccentSecondary
        typeLabel.backgroundColor = UIColor.dragonAccentSecondary.withAlphaComponent(0.1)
        typeLabel.layer.cornerRadius = 4
        typeLabel.layer.masksToBounds = true
        typeLabel.textAlignment = .center
        containerView.addSubview(typeLabel)

        validationImageView.contentMode = .scaleAspectFit
        containerView.addSubview(validationImageView)

        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        containerView.addSubview(chevronImageView)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 20, bottom: 4, right: 20))
        }

        nameLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(12)
            make.trailing.lessThanOrEqualTo(validationImageView.snp.leading).offset(-8)
        }

        detailsLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(12)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }

        typeLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-12)
            make.leading.equalToSuperview().offset(12)
            make.width.greaterThanOrEqualTo(60)
            make.height.equalTo(20)
        }

        validationImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.width.height.equalTo(16)
        }

        chevronImageView.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-12)
            make.width.height.equalTo(16)
        }
    }

    func configure(with strategy: PaceStrategy) {
        nameLabel.text = strategy.name

        let speedKmh = strategy.averageSpeedKmh
        detailsLabel.text = "\(Int(strategy.raceDistance))m • \(TimeFormatter.formatTime(strategy.targetTime)) • \(String(format: "%.1f", speedKmh)) km/h"

        typeLabel.text = strategy.strategyType.displayName.uppercased()

        // Validation indicator
        if strategy.isValid {
            validationImageView.image = UIImage(systemName: "checkmark.circle.fill")
            validationImageView.tintColor = UIColor.dragonSuccess
        } else {
            validationImageView.image = UIImage(systemName: "exclamationmark.triangle.fill")
            validationImageView.tintColor = UIColor.dragonWarning
        }
    }
}
