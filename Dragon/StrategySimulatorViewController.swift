//
//  StrategySimulatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrategySimulatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // 功能卡片
    private let strokeRateSimulatorCard = UIView()
    private let strategyTemplatesCard = UIView()
    private let strokeCalculatorCard = UIView()
    private let performanceSimulatorCard = UIView()
    
    // 当前策略显示
    private let currentStrategyCard = UIView()
    private let currentStrategyLabel = UILabel()
    private let currentStrategyNameLabel = UILabel()
    private let currentStrategyDetailsLabel = UILabel()
    private let editStrategyButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupStrategyManager()
        updateCurrentStrategyDisplay()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateCurrentStrategyDisplay()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "策略模拟器"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "龙舟竞速策略模拟器"
        titleLabel.font = UIFont.systemFont(ofSize: 28, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "科学制定比赛策略，优化划频节奏，预测成绩表现"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupCurrentStrategyCard()
        setupFeatureCards()
    }
    
    private func setupCurrentStrategyCard() {
        currentStrategyCard.backgroundColor = UIColor.dragonCardBackground
        currentStrategyCard.layer.cornerRadius = 12
        currentStrategyCard.layer.borderWidth = 1
        currentStrategyCard.layer.borderColor = UIColor.dragonAccentSecondary.cgColor
        currentStrategyCard.isHidden = true
        contentView.addSubview(currentStrategyCard)
        
        currentStrategyLabel.text = "当前策略"
        currentStrategyLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        currentStrategyLabel.textColor = UIColor.dragonTextPrimary
        currentStrategyCard.addSubview(currentStrategyLabel)
        
        currentStrategyNameLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        currentStrategyNameLabel.textColor = UIColor.dragonAccentSecondary
        currentStrategyCard.addSubview(currentStrategyNameLabel)
        
        currentStrategyDetailsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        currentStrategyDetailsLabel.textColor = UIColor.dragonTextSecondary
        currentStrategyDetailsLabel.numberOfLines = 0
        currentStrategyCard.addSubview(currentStrategyDetailsLabel)
        
        editStrategyButton.setTitle("编辑", for: .normal)
        editStrategyButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        editStrategyButton.setTitleColor(.white, for: .normal)
        editStrategyButton.backgroundColor = UIColor.dragonAccentSecondary
        editStrategyButton.layer.cornerRadius = 8
        editStrategyButton.addTarget(self, action: #selector(editCurrentStrategyPressed), for: .touchUpInside)
        currentStrategyCard.addSubview(editStrategyButton)
    }
    
    private func setupFeatureCards() {
        // 划频模拟器
        setupFeatureCard(
            card: strokeRateSimulatorCard,
            title: "划频模拟器",
            subtitle: "按阶段制定节奏策略",
            description: "输入赛程长度与目标用时，按起步、中段、冲刺阶段划分节奏，自动计算目标划频与换人时机",
            icon: "speedometer",
            color: UIColor.systemBlue,
            action: #selector(openStrokeRateSimulatorPressed)
        )
        
        // 策略模板
        setupFeatureCard(
            card: strategyTemplatesCard,
            title: "节奏策略模板",
            subtitle: "选择战术节奏模板",
            description: "提供均速型、爆发型、冲刺型等多种战术节奏模板，支持自定义并保存为专属方案",
            icon: "doc.text.fill",
            color: UIColor.systemGreen,
            action: #selector(openStrategyTemplatesPressed)
        )
        
        // 桨数计算器
        setupFeatureCard(
            card: strokeCalculatorCard,
            title: "桨数计算器",
            subtitle: "精确计算划桨数据",
            description: "输入单桨推进距离，自动计算全程所需桨数、平均划频及各阶段详细数据",
            icon: "function",
            color: UIColor.systemOrange,
            action: #selector(openStrokeCalculatorPressed)
        )
        
        // 成绩模拟器
        setupFeatureCard(
            card: performanceSimulatorCard,
            title: "成绩模拟器",
            subtitle: "预测比赛表现",
            description: "导入历史队伍数据，基于不同节奏方案模拟预期成绩，提供科学的策略建议",
            icon: "chart.line.uptrend.xyaxis",
            color: UIColor.systemPurple,
            action: #selector(openPerformanceSimulatorPressed)
        )
    }
    
    private func setupFeatureCard(card: UIView, title: String, subtitle: String, description: String, icon: String, color: UIColor, action: Selector) {
        card.backgroundColor = UIColor.dragonCardBackground
        card.layer.cornerRadius = 12
        card.layer.borderWidth = 1
        card.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(card)
        
        // 图标
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit
        card.addSubview(iconImageView)
        
        // 标题
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        card.addSubview(titleLabel)
        
        // 副标题
        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        subtitleLabel.textColor = color
        card.addSubview(subtitleLabel)
        
        // 描述
        let descriptionLabel = UILabel()
        descriptionLabel.text = description
        descriptionLabel.font = UIFont.systemFont(ofSize: 13, weight: .medium)
        descriptionLabel.textColor = UIColor.dragonTextSecondary
        descriptionLabel.numberOfLines = 0
        card.addSubview(descriptionLabel)
        
        // 箭头
        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = UIColor.dragonTextTertiary
        card.addSubview(chevronImageView)
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        card.addGestureRecognizer(tapGesture)
        card.isUserInteractionEnabled = true
        
        // 布局
        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.top.equalTo(subtitleLabel.snp.bottom).offset(8)
            make.trailing.lessThanOrEqualTo(chevronImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        currentStrategyCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(100)
        }
        
        currentStrategyLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        currentStrategyNameLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(editStrategyButton.snp.leading).offset(-16)
        }
        
        currentStrategyDetailsLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStrategyNameLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().offset(16)
            make.trailing.lessThanOrEqualTo(editStrategyButton.snp.leading).offset(-16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }
        
        editStrategyButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().offset(-16)
            make.width.equalTo(60)
            make.height.equalTo(36)
        }
        
        // 功能卡片布局
        let topAnchor = currentStrategyCard.snp.bottom
        
        strokeRateSimulatorCard.snp.makeConstraints { make in
            make.top.equalTo(topAnchor).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
        
        strategyTemplatesCard.snp.makeConstraints { make in
            make.top.equalTo(strokeRateSimulatorCard.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
        
        strokeCalculatorCard.snp.makeConstraints { make in
            make.top.equalTo(strategyTemplatesCard.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
        
        performanceSimulatorCard.snp.makeConstraints { make in
            make.top.equalTo(strokeCalculatorCard.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupStrategyManager() {
        strategyManager.delegate = self
    }

    // MARK: - Actions

    @objc private func editCurrentStrategyPressed() {
        guard let strategy = strategyManager.currentStrategy else { return }
        let editorVC = StrategyEditorViewController(strategy: strategy)
        let navController = UINavigationController(rootViewController: editorVC)
        present(navController, animated: true)
    }

    @objc private func openStrokeRateSimulatorPressed() {
        let simulatorVC = StrokeRateSimulatorViewController()
        navigationController?.pushViewController(simulatorVC, animated: true)
    }

    @objc private func openStrategyTemplatesPressed() {
        let templatesVC = StrategyTemplatesViewController()
        navigationController?.pushViewController(templatesVC, animated: true)
    }

    @objc private func openStrokeCalculatorPressed() {
        let calculatorVC = StrokeCalculatorViewController()
        navigationController?.pushViewController(calculatorVC, animated: true)
    }

    @objc private func openPerformanceSimulatorPressed() {
        let performanceVC = PerformanceSimulatorViewController()
        navigationController?.pushViewController(performanceVC, animated: true)
    }

    // MARK: - UI Updates

    private func updateCurrentStrategyDisplay() {
        if let strategy = strategyManager.currentStrategy {
            currentStrategyCard.isHidden = false
            currentStrategyNameLabel.text = strategy.name

            let details = "\(Int(strategy.raceDistance))m • \(TimeFormatter.formatTime(strategy.targetTime)) • \(strategy.phases.count)个阶段"
            currentStrategyDetailsLabel.text = details

            // 更新约束
            updateConstraintsForCurrentStrategy(hidden: false)
        } else {
            currentStrategyCard.isHidden = true
            updateConstraintsForCurrentStrategy(hidden: true)
        }
    }

    private func updateConstraintsForCurrentStrategy(hidden: Bool) {
        let topAnchor = hidden ? subtitleLabel.snp.bottom : currentStrategyCard.snp.bottom

        strokeRateSimulatorCard.snp.remakeConstraints { make in
            make.top.equalTo(topAnchor).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.greaterThanOrEqualTo(120)
        }
    }
}

// MARK: - StrategyManagerDelegate

extension StrategySimulatorViewController: StrategyManagerDelegate {
    func strategyManager(_ manager: StrategyManager, didUpdateStrategy strategy: RaceStrategy) {
        DispatchQueue.main.async {
            self.updateCurrentStrategyDisplay()
        }
    }

    func strategyManager(_ manager: StrategyManager, didDeleteStrategy strategy: RaceStrategy) {
        DispatchQueue.main.async {
            self.updateCurrentStrategyDisplay()
        }
    }

    func strategyManager(_ manager: StrategyManager, didSetCurrentStrategy strategy: RaceStrategy?) {
        DispatchQueue.main.async {
            self.updateCurrentStrategyDisplay()
        }
    }
}
