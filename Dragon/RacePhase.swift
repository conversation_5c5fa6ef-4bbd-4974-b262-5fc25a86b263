//
//  RacePhase.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation
import UIKit

// 比赛阶段类型
enum PhaseType: String, CaseIterable, Codable {
    case start = "start"        // 起步阶段
    case middle = "middle"      // 中段阶段
    case sprint = "sprint"      // 冲刺阶段
    
    var displayName: String {
        switch self {
        case .start: return "起步阶段"
        case .middle: return "中段阶段"
        case .sprint: return "冲刺阶段"
        }
    }
    
    var color: UIColor {
        switch self {
        case .start: return UIColor.systemRed
        case .middle: return UIColor.systemYellow
        case .sprint: return UIColor.systemOrange
        }
    }
}

// 比赛阶段数据模型
class RacePhase: NSObject, Codable {
    let id: UUID
    var name: String
    var phaseType: PhaseType
    var duration: TimeInterval      // 阶段持续时间（秒）
    var distance: Double           // 阶段距离（米）
    var targetStrokeRate: Double   // 目标划频（SPM）
    var strokeDistance: Double     // 单桨距离（米）
    var allowsSubstitution: Bool   // 是否允许换人
    var substitutionTiming: TimeInterval? // 换人时机（秒）
    
    init(name: String, 
         phaseType: PhaseType, 
         duration: TimeInterval, 
         distance: Double, 
         targetStrokeRate: Double, 
         strokeDistance: Double = 2.5,
         allowsSubstitution: Bool = false) {
        self.id = UUID()
        self.name = name
        self.phaseType = phaseType
        self.duration = duration
        self.distance = distance
        self.targetStrokeRate = targetStrokeRate
        self.strokeDistance = strokeDistance
        self.allowsSubstitution = allowsSubstitution
        super.init()
    }
    
    // 计算阶段所需桨数
    var totalStrokes: Int {
        return Int(ceil(distance / strokeDistance))
    }
    
    // 计算实际划频（基于桨数和时间）
    var actualStrokeRate: Double {
        return (Double(totalStrokes) / duration) * 60
    }
    
    // 计算平均速度（m/s）
    var averageSpeed: Double {
        return distance / duration
    }
    
    // 计算平均速度（km/h）
    var averageSpeedKmh: Double {
        return averageSpeed * 3.6
    }
    
    // 验证阶段数据是否合理
    var isValid: Bool {
        return duration > 0 && 
               distance > 0 && 
               targetStrokeRate > 0 && 
               strokeDistance > 0 &&
               targetStrokeRate >= 30 && 
               targetStrokeRate <= 120
    }
    
    // 计算与目标划频的偏差
    var strokeRateDeviation: Double {
        return abs(actualStrokeRate - targetStrokeRate)
    }
}

// 策略类型
enum StrategyType: String, CaseIterable, Codable {
    case evenPace = "evenPace"           // 均速型
    case startBurst = "startBurst"       // 起步爆发型
    case finishSprint = "finishSprint"   // 冲刺终结型
    case negative = "negative"           // 负分段（后程加速）
    case positive = "positive"           // 正分段（前程快速）
    case custom = "custom"               // 自定义
    
    var displayName: String {
        switch self {
        case .evenPace: return "均速型"
        case .startBurst: return "起步爆发型"
        case .finishSprint: return "冲刺终结型"
        case .negative: return "负分段"
        case .positive: return "正分段"
        case .custom: return "自定义"
        }
    }
    
    var description: String {
        switch self {
        case .evenPace: return "全程保持稳定节奏，适合长距离比赛"
        case .startBurst: return "起步快速抢位，中段稳定，适合短距离"
        case .finishSprint: return "前段保存体力，最后阶段全力冲刺"
        case .negative: return "前段较慢，后段逐渐加速，考验体能分配"
        case .positive: return "前段快速，后段保持，需要良好的体能基础"
        case .custom: return "根据队伍特点自定义节奏分配"
        }
    }
}

// 比赛策略数据模型
class RaceStrategy: NSObject, Codable {
    let id: UUID
    var name: String
    var strategyType: StrategyType
    var raceDistance: Double       // 比赛总距离
    var targetTime: TimeInterval   // 目标总时间
    var phases: [RacePhase]        // 比赛阶段
    var createdAt: Date
    var lastModified: Date
    
    init(name: String, 
         strategyType: StrategyType, 
         raceDistance: Double, 
         targetTime: TimeInterval) {
        self.id = UUID()
        self.name = name
        self.strategyType = strategyType
        self.raceDistance = raceDistance
        self.targetTime = targetTime
        self.phases = []
        self.createdAt = Date()
        self.lastModified = Date()
        super.init()
    }
    
    // 计算总桨数
    var totalStrokes: Int {
        return phases.reduce(0) { $0 + $1.totalStrokes }
    }
    
    // 计算平均划频
    var averageStrokeRate: Double {
        let totalTime = phases.reduce(0) { $0 + $1.duration }
        guard totalTime > 0 else { return 0 }
        return (Double(totalStrokes) / totalTime) * 60
    }
    
    // 计算平均速度（km/h）
    var averageSpeedKmh: Double {
        guard targetTime > 0 else { return 0 }
        return (raceDistance / targetTime) * 3.6
    }
    
    // 计算实际总时间
    var actualTotalTime: TimeInterval {
        return phases.reduce(0) { $0 + $1.duration }
    }
    
    // 计算实际总距离
    var actualTotalDistance: Double {
        return phases.reduce(0) { $0 + $1.distance }
    }
    
    // 验证策略是否有效
    var isValid: Bool {
        guard !phases.isEmpty else { return false }
        
        // 检查时间和距离是否匹配
        let timeDiff = abs(actualTotalTime - targetTime)
        let distanceDiff = abs(actualTotalDistance - raceDistance)
        
        return timeDiff <= 5.0 && // 允许5秒误差
               distanceDiff <= 10.0 && // 允许10米误差
               phases.allSatisfy { $0.isValid }
    }
    
    // 获取换人时机列表
    var substitutionTimings: [TimeInterval] {
        var timings: [TimeInterval] = []
        var currentTime: TimeInterval = 0
        
        for phase in phases {
            if phase.allowsSubstitution, let timing = phase.substitutionTiming {
                timings.append(currentTime + timing)
            }
            currentTime += phase.duration
        }
        
        return timings
    }
    
    // 更新修改时间
    func updateModificationTime() {
        lastModified = Date()
    }
}
