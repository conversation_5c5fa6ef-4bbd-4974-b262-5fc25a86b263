//
//  PhaseConfigView.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

// 阶段数据结构
struct PhaseData {
    let type: PhaseType
    let name: String
    let durationRatio: Double
    let strokeRate: Double
    let allowsSubstitution: Bool
}

// 阶段配置视图代理
protocol PhaseConfigViewDelegate: AnyObject {
    func phaseConfigView(_ view: PhaseConfigView, didRequestRemoval: Bool)
    func phaseConfigViewDidChange(_ view: PhaseConfigView)
}

class PhaseConfigView: UIView {
    
    // MARK: - UI Components
    
    private let containerView = UIView()
    private let headerView = UIView()
    private let phaseTypeLabel = UILabel()
    private let removeButton = UIButton(type: .system)
    
    private let nameTextField = UITextField()
    private let durationRatioSlider = UISlider()
    private let durationRatioLabel = UILabel()
    private let strokeRateTextField = UITextField()
    private let substitutionSwitch = UISwitch()
    private let substitutionLabel = UILabel()
    
    // MARK: - Properties
    
    weak var delegate: PhaseConfigViewDelegate?
    private let phaseType: PhaseType
    
    // MARK: - Initialization
    
    init(phaseType: PhaseType) {
        self.phaseType = phaseType
        super.init(frame: .zero)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // Container
        containerView.backgroundColor = UIColor.dragonSecondaryBackground
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = phaseType.color.cgColor
        addSubview(containerView)
        
        // Header
        headerView.backgroundColor = phaseType.color.withAlphaComponent(0.1)
        containerView.addSubview(headerView)
        
        phaseTypeLabel.text = phaseType.displayName
        phaseTypeLabel.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        phaseTypeLabel.textColor = phaseType.color
        headerView.addSubview(phaseTypeLabel)
        
        removeButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        removeButton.tintColor = UIColor.systemRed
        removeButton.addTarget(self, action: #selector(removePressed), for: .touchUpInside)
        headerView.addSubview(removeButton)
        
        // Name field
        nameTextField.placeholder = "Phase Name"
        nameTextField.text = phaseType.displayName
        setupTextField(nameTextField)
        nameTextField.addTarget(self, action: #selector(valueChanged), for: .editingChanged)
        containerView.addSubview(nameTextField)

        // Duration ratio slider
        durationRatioSlider.minimumValue = 0.1
        durationRatioSlider.maximumValue = 0.8
        durationRatioSlider.value = 0.2
        durationRatioSlider.addTarget(self, action: #selector(sliderChanged), for: .valueChanged)
        containerView.addSubview(durationRatioSlider)

        durationRatioLabel.text = "Time Ratio: 20%"
        durationRatioLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        durationRatioLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(durationRatioLabel)

        // Stroke rate field
        strokeRateTextField.placeholder = "Stroke Rate (SPM)"
        strokeRateTextField.text = "65"
        strokeRateTextField.keyboardType = .numberPad
        setupTextField(strokeRateTextField)
        strokeRateTextField.addTarget(self, action: #selector(valueChanged), for: .editingChanged)
        containerView.addSubview(strokeRateTextField)

        // Substitution switch
        substitutionLabel.text = "Allow Substitution"
        substitutionLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        substitutionLabel.textColor = UIColor.dragonTextSecondary
        containerView.addSubview(substitutionLabel)
        
        substitutionSwitch.isOn = false
        substitutionSwitch.onTintColor = UIColor.dragonAccent
        substitutionSwitch.addTarget(self, action: #selector(valueChanged), for: .valueChanged)
        containerView.addSubview(substitutionSwitch)
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonCardBackground
        textField.layer.cornerRadius = 6
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        headerView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(36)
        }
        
        phaseTypeLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
        }
        
        removeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        nameTextField.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(12)
            make.height.equalTo(40)
        }

        durationRatioLabel.snp.makeConstraints { make in
            make.top.equalTo(nameTextField.snp.bottom).offset(12)
            make.leading.equalToSuperview().offset(12)
        }

        durationRatioSlider.snp.makeConstraints { make in
            make.top.equalTo(durationRatioLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(12)
            make.height.equalTo(30)
        }

        strokeRateTextField.snp.makeConstraints { make in
            make.top.equalTo(durationRatioSlider.snp.bottom).offset(12)
            make.leading.equalToSuperview().offset(12)
            make.width.equalTo(120)
            make.height.equalTo(40)
        }

        substitutionLabel.snp.makeConstraints { make in
            make.centerY.equalTo(strokeRateTextField)
            make.leading.equalTo(strokeRateTextField.snp.trailing).offset(20)
        }

        substitutionSwitch.snp.makeConstraints { make in
            make.centerY.equalTo(strokeRateTextField)
            make.trailing.equalToSuperview().offset(-12)
            make.bottom.lessThanOrEqualToSuperview().offset(-12)
        }
    }
    
    // MARK: - Actions
    
    @objc private func removePressed() {
        delegate?.phaseConfigView(self, didRequestRemoval: true)
    }
    
    @objc private func sliderChanged() {
        let percentage = Int(durationRatioSlider.value * 100)
        durationRatioLabel.text = "Time Ratio: \(percentage)%"
        delegate?.phaseConfigViewDidChange(self)
    }
    
    @objc private func valueChanged() {
        delegate?.phaseConfigViewDidChange(self)
    }
    
    // MARK: - Public Methods
    
    func configure(name: String, durationRatio: Double, strokeRate: Double) {
        nameTextField.text = name
        durationRatioSlider.value = Float(durationRatio)
        strokeRateTextField.text = String(Int(strokeRate))
        
        let percentage = Int(durationRatio * 100)
        durationRatioLabel.text = "Time Ratio: \(percentage)%"
    }
    
    func getPhaseData() -> PhaseData {
        return PhaseData(
            type: phaseType,
            name: nameTextField.text ?? phaseType.displayName,
            durationRatio: Double(durationRatioSlider.value),
            strokeRate: Double(strokeRateTextField.text ?? "65") ?? 65,
            allowsSubstitution: substitutionSwitch.isOn
        )
    }
}
