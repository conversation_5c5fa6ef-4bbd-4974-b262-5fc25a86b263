//
//  PerformanceSimulatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class PerformanceSimulatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // 队伍选择卡片
    private let teamSelectionCard = UIView()
    private let teamSelectionLabel = UILabel()
    private let teamPickerView = UIPickerView()
    private let teamInfoLabel = UILabel()
    
    // 策略选择卡片
    private let strategySelectionCard = UIView()
    private let strategySelectionLabel = UILabel()
    private let strategyPickerView = UIPickerView()
    private let strategyInfoLabel = UILabel()
    
    // 模拟结果卡片
    private let resultsCard = UIView()
    private let resultsLabel = UILabel()
    private let predictedTimeLabel = UILabel()
    private let confidenceLabel = UILabel()
    private let recommendationsLabel = UILabel()
    private let riskFactorsLabel = UILabel()
    
    // 操作按钮
    private let simulateButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private let raceManager = RaceManager.shared
    private let strategyManager = StrategyManager.shared
    
    private var availableTeams: [Team] = []
    private var availableStrategies: [RaceStrategy] = []
    private var selectedTeam: Team?
    private var selectedStrategy: RaceStrategy?
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "成绩模拟器"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "预测比赛表现"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "基于历史数据和策略分析，预测队伍在不同节奏方案下的表现"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupTeamSelectionCard()
        setupStrategySelectionCard()
        setupResultsCard()
        setupSimulateButton()
    }
    
    private func setupTeamSelectionCard() {
        teamSelectionCard.backgroundColor = UIColor.dragonCardBackground
        teamSelectionCard.layer.cornerRadius = 12
        teamSelectionCard.layer.borderWidth = 1
        teamSelectionCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(teamSelectionCard)
        
        teamSelectionLabel.text = "选择队伍"
        teamSelectionLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        teamSelectionLabel.textColor = UIColor.dragonTextPrimary
        teamSelectionCard.addSubview(teamSelectionLabel)
        
        teamPickerView.delegate = self
        teamPickerView.dataSource = self
        teamSelectionCard.addSubview(teamPickerView)
        
        teamInfoLabel.text = "请选择要分析的队伍"
        teamInfoLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        teamInfoLabel.textColor = UIColor.dragonTextSecondary
        teamInfoLabel.numberOfLines = 0
        teamSelectionCard.addSubview(teamInfoLabel)
        
        // 约束
        teamSelectionLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        teamPickerView.snp.makeConstraints { make in
            make.top.equalTo(teamSelectionLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(120)
        }
        
        teamInfoLabel.snp.makeConstraints { make in
            make.top.equalTo(teamPickerView.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupStrategySelectionCard() {
        strategySelectionCard.backgroundColor = UIColor.dragonCardBackground
        strategySelectionCard.layer.cornerRadius = 12
        strategySelectionCard.layer.borderWidth = 1
        strategySelectionCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(strategySelectionCard)
        
        strategySelectionLabel.text = "选择策略"
        strategySelectionLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        strategySelectionLabel.textColor = UIColor.dragonTextPrimary
        strategySelectionCard.addSubview(strategySelectionLabel)
        
        strategyPickerView.delegate = self
        strategyPickerView.dataSource = self
        strategySelectionCard.addSubview(strategyPickerView)
        
        strategyInfoLabel.text = "请选择要测试的策略"
        strategyInfoLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        strategyInfoLabel.textColor = UIColor.dragonTextSecondary
        strategyInfoLabel.numberOfLines = 0
        strategySelectionCard.addSubview(strategyInfoLabel)
        
        // 约束
        strategySelectionLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        strategyPickerView.snp.makeConstraints { make in
            make.top.equalTo(strategySelectionLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(120)
        }
        
        strategyInfoLabel.snp.makeConstraints { make in
            make.top.equalTo(strategyPickerView.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupResultsCard() {
        resultsCard.backgroundColor = UIColor.dragonCardBackground
        resultsCard.layer.cornerRadius = 12
        resultsCard.layer.borderWidth = 1
        resultsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(resultsCard)
        
        resultsLabel.text = "预测结果"
        resultsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        resultsLabel.textColor = UIColor.dragonTextPrimary
        resultsCard.addSubview(resultsLabel)
        
        predictedTimeLabel.text = "预测成绩: --"
        predictedTimeLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        predictedTimeLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(predictedTimeLabel)
        
        confidenceLabel.text = "置信度: --%"
        confidenceLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        confidenceLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(confidenceLabel)
        
        recommendationsLabel.text = "策略建议: --"
        recommendationsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        recommendationsLabel.textColor = UIColor.dragonTextSecondary
        recommendationsLabel.numberOfLines = 0
        resultsCard.addSubview(recommendationsLabel)
        
        riskFactorsLabel.text = "风险因素: --"
        riskFactorsLabel.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        riskFactorsLabel.textColor = UIColor.systemOrange
        riskFactorsLabel.numberOfLines = 0
        resultsCard.addSubview(riskFactorsLabel)
        
        // 约束
        resultsLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        predictedTimeLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        confidenceLabel.snp.makeConstraints { make in
            make.top.equalTo(predictedTimeLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        recommendationsLabel.snp.makeConstraints { make in
            make.top.equalTo(confidenceLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        riskFactorsLabel.snp.makeConstraints { make in
            make.top.equalTo(recommendationsLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupSimulateButton() {
        simulateButton.setTitle("开始模拟", for: .normal)
        simulateButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        simulateButton.setTitleColor(.white, for: .normal)
        simulateButton.backgroundColor = UIColor.dragonAccent
        simulateButton.layer.cornerRadius = 12
        simulateButton.isEnabled = false
        simulateButton.alpha = 0.6
        simulateButton.addTarget(self, action: #selector(simulatePressed), for: .touchUpInside)
        contentView.addSubview(simulateButton)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        teamSelectionCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        strategySelectionCard.snp.makeConstraints { make in
            make.top.equalTo(teamSelectionCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(strategySelectionCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        simulateButton.snp.makeConstraints { make in
            make.top.equalTo(resultsCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func loadData() {
        // 加载可用队伍（从历史比赛中获取）
        let allTeams = raceManager.completedRaces.flatMap { $0.teams }
        availableTeams = Array(Set(allTeams.map { $0.id })).compactMap { teamId in
            allTeams.first { $0.id == teamId }
        }
        
        // 加载可用策略
        availableStrategies = strategyManager.customStrategies
        
        // 刷新picker view
        teamPickerView.reloadAllComponents()
        strategyPickerView.reloadAllComponents()
        
        // 检查是否可以开始模拟
        updateSimulateButtonState()
    }

    // MARK: - Actions

    @objc private func simulatePressed() {
        guard let team = selectedTeam,
              let strategy = selectedStrategy else { return }

        // 执行成绩预测
        let prediction = strategyManager.predictPerformance(for: team, using: strategy)

        // 更新结果显示
        updateResults(with: prediction)
    }

    // MARK: - Helper Methods

    private func updateSimulateButtonState() {
        let canSimulate = selectedTeam != nil && selectedStrategy != nil
        simulateButton.isEnabled = canSimulate
        simulateButton.alpha = canSimulate ? 1.0 : 0.6
    }

    private func updateTeamInfo() {
        guard let team = selectedTeam else {
            teamInfoLabel.text = "请选择要分析的队伍"
            return
        }

        // 获取队伍历史数据
        let teamHistory = raceManager.completedRaces.compactMap { race in
            race.teams.first { $0.id == team.id }
        }

        if teamHistory.isEmpty {
            teamInfoLabel.text = "该队伍暂无历史比赛数据"
        } else {
            let raceCount = teamHistory.count
            let averageTime = teamHistory.compactMap { $0.finishTime }.reduce(0, +) / Double(teamHistory.count)
            let bestTime = teamHistory.compactMap { $0.finishTime }.min() ?? 0

            teamInfoLabel.text = "历史比赛: \(raceCount)场\n平均成绩: \(TimeFormatter.formatTime(averageTime))\n最佳成绩: \(TimeFormatter.formatTime(bestTime))"
        }
    }

    private func updateStrategyInfo() {
        guard let strategy = selectedStrategy else {
            strategyInfoLabel.text = "请选择要测试的策略"
            return
        }

        strategyInfoLabel.text = "\(strategy.strategyType.displayName)\n距离: \(Int(strategy.raceDistance))m\n目标时间: \(TimeFormatter.formatTime(strategy.targetTime))\n阶段数: \(strategy.phases.count)"
    }

    private func updateResults(with prediction: PerformancePrediction) {
        predictedTimeLabel.text = "预测成绩: \(prediction.formattedTime)"
        predictedTimeLabel.textColor = UIColor.dragonAccentSecondary

        confidenceLabel.text = "置信度: \(prediction.confidencePercentage)%"
        confidenceLabel.textColor = getConfidenceColor(prediction.confidence)

        if prediction.recommendations.isEmpty {
            recommendationsLabel.text = "策略建议: 当前策略配置合理"
        } else {
            recommendationsLabel.text = "策略建议:\n" + prediction.recommendations.joined(separator: "\n")
        }

        if prediction.riskFactors.isEmpty {
            riskFactorsLabel.text = "风险因素: 无特殊风险"
            riskFactorsLabel.textColor = UIColor.systemGreen
        } else {
            riskFactorsLabel.text = "风险因素:\n" + prediction.riskFactors.joined(separator: "\n")
            riskFactorsLabel.textColor = UIColor.systemOrange
        }
    }

    private func getConfidenceColor(_ confidence: Double) -> UIColor {
        switch confidence {
        case 0.8...: return UIColor.systemGreen
        case 0.6..<0.8: return UIColor.systemBlue
        case 0.4..<0.6: return UIColor.systemOrange
        default: return UIColor.systemRed
        }
    }
}

// MARK: - UIPickerViewDataSource & UIPickerViewDelegate

extension PerformanceSimulatorViewController: UIPickerViewDataSource, UIPickerViewDelegate {
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 1
    }

    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        if pickerView == teamPickerView {
            return availableTeams.count
        } else if pickerView == strategyPickerView {
            return availableStrategies.count
        }
        return 0
    }

    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        if pickerView == teamPickerView {
            guard row < availableTeams.count else { return nil }
            return availableTeams[row].name
        } else if pickerView == strategyPickerView {
            guard row < availableStrategies.count else { return nil }
            return availableStrategies[row].name
        }
        return nil
    }

    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        if pickerView == teamPickerView {
            if row < availableTeams.count {
                selectedTeam = availableTeams[row]
                updateTeamInfo()
            }
        } else if pickerView == strategyPickerView {
            if row < availableStrategies.count {
                selectedStrategy = availableStrategies[row]
                updateStrategyInfo()
            }
        }

        updateSimulateButtonState()
    }
}
