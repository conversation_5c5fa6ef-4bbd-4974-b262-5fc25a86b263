//
//  SimpleChartView.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit

class SimpleChartView: UIView {
    
    // MARK: - Properties
    
    private var series: [ChartSeries] = []
    private let padding: CGFloat = 20
    private let axisColor = UIColor.dragonTextTertiary
    private let gridColor = UIColor.dragonBorder
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = .clear
    }
    
    // MARK: - Public Methods
    
    func setSeries(_ series: [ChartSeries]) {
        self.series = series
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        guard let context = UIGraphicsGetCurrentContext(),
              !series.isEmpty else { return }
        
        let chartRect = CGRect(
            x: padding,
            y: padding,
            width: rect.width - padding * 2,
            height: rect.height - padding * 2
        )
        
        // Calculate data bounds
        let allPoints = series.flatMap { $0.points }
        guard !allPoints.isEmpty else { return }
        
        let minX = allPoints.map { $0.x }.min() ?? 0
        let maxX = allPoints.map { $0.x }.max() ?? 1
        let minY = allPoints.map { $0.y }.min() ?? 0
        let maxY = allPoints.map { $0.y }.max() ?? 1
        
        // Add some padding to Y axis
        let yRange = maxY - minY
        let paddedMinY = minY - yRange * 0.1
        let paddedMaxY = maxY + yRange * 0.1
        
        // Draw grid and axes
        drawGrid(context: context, rect: chartRect, minX: minX, maxX: maxX, minY: paddedMinY, maxY: paddedMaxY)
        
        // Draw series
        for chartSeries in series {
            drawSeries(context: context, series: chartSeries, rect: chartRect, minX: minX, maxX: maxX, minY: paddedMinY, maxY: paddedMaxY)
        }
        
        // Draw axes labels
        drawAxisLabels(context: context, rect: chartRect, minX: minX, maxX: maxX, minY: paddedMinY, maxY: paddedMaxY)
    }
    
    private func drawGrid(context: CGContext, rect: CGRect, minX: Double, maxX: Double, minY: Double, maxY: Double) {
        context.setStrokeColor(gridColor.cgColor)
        context.setLineWidth(0.5)
        
        // Vertical grid lines
        let xSteps = 5
        for i in 0...xSteps {
            let x = rect.minX + (rect.width / CGFloat(xSteps)) * CGFloat(i)
            context.move(to: CGPoint(x: x, y: rect.minY))
            context.addLine(to: CGPoint(x: x, y: rect.maxY))
        }
        
        // Horizontal grid lines
        let ySteps = 5
        for i in 0...ySteps {
            let y = rect.minY + (rect.height / CGFloat(ySteps)) * CGFloat(i)
            context.move(to: CGPoint(x: rect.minX, y: y))
            context.addLine(to: CGPoint(x: rect.maxX, y: y))
        }
        
        context.strokePath()
        
        // Draw main axes
        context.setStrokeColor(axisColor.cgColor)
        context.setLineWidth(1.0)
        
        // X axis
        context.move(to: CGPoint(x: rect.minX, y: rect.maxY))
        context.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        
        // Y axis
        context.move(to: CGPoint(x: rect.minX, y: rect.minY))
        context.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        
        context.strokePath()
    }
    
    private func drawSeries(context: CGContext, series: ChartSeries, rect: CGRect, minX: Double, maxX: Double, minY: Double, maxY: Double) {
        guard series.points.count > 1 else { return }
        
        context.setStrokeColor(series.color.cgColor)
        context.setLineWidth(2.0)
        
        let xRange = maxX - minX
        let yRange = maxY - minY
        
        // Convert first point
        let firstPoint = series.points[0]
        let startX = rect.minX + (CGFloat((firstPoint.x - minX) / xRange) * rect.width)
        let startY = rect.maxY - (CGFloat((firstPoint.y - minY) / yRange) * rect.height)
        
        context.move(to: CGPoint(x: startX, y: startY))
        
        // Draw line to each subsequent point
        for i in 1..<series.points.count {
            let point = series.points[i]
            let x = rect.minX + (CGFloat((point.x - minX) / xRange) * rect.width)
            let y = rect.maxY - (CGFloat((point.y - minY) / yRange) * rect.height)
            context.addLine(to: CGPoint(x: x, y: y))
        }
        
        context.strokePath()
        
        // Draw data points
        context.setFillColor(series.color.cgColor)
        
        for point in series.points {
            let x = rect.minX + (CGFloat((point.x - minX) / xRange) * rect.width)
            let y = rect.maxY - (CGFloat((point.y - minY) / yRange) * rect.height)
            
            let pointRect = CGRect(x: x - 3, y: y - 3, width: 6, height: 6)
            context.fillEllipse(in: pointRect)
        }
    }
    
    private func drawAxisLabels(context: CGContext, rect: CGRect, minX: Double, maxX: Double, minY: Double, maxY: Double) {
        let labelFont = UIFont.systemFont(ofSize: 10)
        let labelColor = UIColor.dragonTextTertiary
        
        // Y axis labels (times)
        let ySteps = 5
        for i in 0...ySteps {
            let value = minY + (maxY - minY) * Double(i) / Double(ySteps)
            let y = rect.maxY - (rect.height / CGFloat(ySteps)) * CGFloat(i)
            
            let timeString = TimeFormatter.formatTime(value)
            let labelSize = timeString.size(withAttributes: [.font: labelFont])
            let labelRect = CGRect(
                x: rect.minX - labelSize.width - 5,
                y: y - labelSize.height / 2,
                width: labelSize.width,
                height: labelSize.height
            )
            
            timeString.draw(in: labelRect, withAttributes: [
                .font: labelFont,
                .foregroundColor: labelColor
            ])
        }
        
        // X axis labels (race numbers)
        let xSteps = min(5, Int(maxX - minX))
        if xSteps > 0 {
            for i in 0...xSteps {
                let value = minX + (maxX - minX) * Double(i) / Double(xSteps)
                let x = rect.minX + (rect.width / CGFloat(xSteps)) * CGFloat(i)
                
                let raceString = "Race \(Int(value + 1))"
                let labelSize = raceString.size(withAttributes: [.font: labelFont])
                let labelRect = CGRect(
                    x: x - labelSize.width / 2,
                    y: rect.maxY + 5,
                    width: labelSize.width,
                    height: labelSize.height
                )
                
                raceString.draw(in: labelRect, withAttributes: [
                    .font: labelFont,
                    .foregroundColor: labelColor
                ])
            }
        }
    }
}

// MARK: - Bar Chart View

class SimpleBarChartView: UIView {
    
    // MARK: - Properties
    
    private var data: [BarChartData] = []
    private let padding: CGFloat = 20
    private let barSpacing: CGFloat = 8
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = .clear
    }
    
    // MARK: - Public Methods
    
    func setData(_ data: [BarChartData]) {
        self.data = data
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        guard let context = UIGraphicsGetCurrentContext(),
              !data.isEmpty else { return }
        
        let chartRect = CGRect(
            x: padding,
            y: padding,
            width: rect.width - padding * 2,
            height: rect.height - padding * 2
        )
        
        let maxValue = data.map { $0.value }.max() ?? 1
        let barWidth = (chartRect.width - CGFloat(data.count - 1) * barSpacing) / CGFloat(data.count)
        
        for (index, barData) in data.enumerated() {
            let barHeight = (barData.value / maxValue) * chartRect.height
            let x = chartRect.minX + CGFloat(index) * (barWidth + barSpacing)
            let y = chartRect.maxY - barHeight
            
            let barRect = CGRect(x: x, y: y, width: barWidth, height: barHeight)
            
            context.setFillColor(barData.color.cgColor)
            context.fill(barRect)
            
            // Draw label
            let labelFont = UIFont.systemFont(ofSize: 10)
            let labelSize = barData.label.size(withAttributes: [.font: labelFont])
            let labelRect = CGRect(
                x: x + (barWidth - labelSize.width) / 2,
                y: chartRect.maxY + 5,
                width: labelSize.width,
                height: labelSize.height
            )
            
            barData.label.draw(in: labelRect, withAttributes: [
                .font: labelFont,
                .foregroundColor: UIColor.dragonTextTertiary
            ])
        }
    }
}

// MARK: - Bar Chart Data

struct BarChartData {
    let label: String
    let value: Double
    let color: UIColor
    
    init(label: String, value: Double, color: UIColor = UIColor.dragonAccent) {
        self.label = label
        self.value = value
        self.color = color
    }
}
