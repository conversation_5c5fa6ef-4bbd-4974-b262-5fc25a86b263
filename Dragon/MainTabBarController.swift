//
//  MainTabBarController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupTabBar()
        setupViewControllers()
    }
    
    private func setupTabBar() {
        // Configure tab bar appearance
        tabBar.backgroundColor = UIColor.dragonDarkBackground
        tabBar.barTintColor = UIColor.dragonDarkBackground
        tabBar.tintColor = UIColor.dragonAccent
        tabBar.unselectedItemTintColor = UIColor.dragonTextTertiary
        
        // Configure tab bar appearance for iOS 15+
        if #available(iOS 15.0, *) {
            let appearance = UITabBarAppearance()
            appearance.configureWithOpaqueBackground()
            appearance.backgroundColor = UIColor.dragonDarkBackground
            
            // Normal state
            appearance.stackedLayoutAppearance.normal.iconColor = UIColor.dragonTextTertiary
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
                .foregroundColor: UIColor.dragonTextTertiary
            ]
            
            // Selected state
            appearance.stackedLayoutAppearance.selected.iconColor = UIColor.dragonAccent
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
                .foregroundColor: UIColor.dragonAccent
            ]
            
            tabBar.standardAppearance = appearance
            tabBar.scrollEdgeAppearance = appearance
        }
    }
    
    private func setupViewControllers() {
        // 1. Race Timer Module
        let raceTimerVC = MainViewController()
        let raceTimerNav = UINavigationController(rootViewController: raceTimerVC)
        raceTimerNav.tabBarItem = UITabBarItem(
            title: "Timer",
            image: UIImage(systemName: "stopwatch"),
            selectedImage: UIImage(systemName: "stopwatch.fill")
        )
        
        // 2. Strategy Simulator Module
        let strategyVC = StrategySimulatorViewController()
        let strategyNav = UINavigationController(rootViewController: strategyVC)
        strategyNav.tabBarItem = UITabBarItem(
            title: "Strategy",
            image: UIImage(systemName: "chart.line.uptrend.xyaxis"),
            selectedImage: UIImage(systemName: "chart.line.uptrend.xyaxis")
        )
        
        // 3. Race Analysis Module
        let analysisVC = RaceAnalysisViewController()
        let analysisNav = UINavigationController(rootViewController: analysisVC)
        analysisNav.tabBarItem = UITabBarItem(
            title: "Analysis",
            image: UIImage(systemName: "chart.bar"),
            selectedImage: UIImage(systemName: "chart.bar.fill")
        )
        
        // Set view controllers
        viewControllers = [raceTimerNav, strategyNav, analysisNav]
        
        // Set default selected tab
        selectedIndex = 0
    }
}
