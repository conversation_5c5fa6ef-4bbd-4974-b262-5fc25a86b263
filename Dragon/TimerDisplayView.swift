//
//  TimerDisplayView.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class TimerDisplayView: UIView {
    
    // MARK: - UI Components
    
    private let containerView = UIView()
    private let timeLabel = UILabel()
    private let statusLabel = UILabel()
    private let iconImageView = UIImageView()
    
    // MARK: - Properties
    
    private var currentTime: TimeInterval = 0
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // Container view
        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 16
        containerView.layer.borderWidth = 2
        containerView.layer.borderColor = UIColor.dragonAccent.cgColor
        addSubview(containerView)
        
        // Icon
        iconImageView.image = UIImage(systemName: "stopwatch.fill")
        iconImageView.tintColor = UIColor.dragonAccent
        iconImageView.contentMode = .scaleAspectFit
        containerView.addSubview(iconImageView)
        
        // Time label
        timeLabel.text = "0.000"
        timeLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 36, weight: .bold)
        timeLabel.textColor = UIColor.dragonTextPrimary
        timeLabel.textAlignment = .center
        containerView.addSubview(timeLabel)
        
        // Status label
        statusLabel.text = "Race Timer"
        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        statusLabel.textColor = UIColor.dragonTextSecondary
        statusLabel.textAlignment = .center
        containerView.addSubview(statusLabel)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(timeLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Public Methods
    
    /// Update the displayed time
    /// - Parameter time: Time interval in seconds
    func updateTime(_ time: TimeInterval) {
        currentTime = time
        timeLabel.text = TimeFormatter.formatTimerTime(time)
        
        // Add subtle animation for time updates
        UIView.transition(with: timeLabel, duration: 0.1, options: .transitionCrossDissolve, animations: {
            // Animation handled by transition
        })
    }
    
    /// Set the timer status
    /// - Parameter status: Status text to display
    func setStatus(_ status: String) {
        statusLabel.text = status
    }
    
    /// Animate the timer for race start
    func animateRaceStart() {
        // Pulse animation
        UIView.animate(withDuration: 0.3, delay: 0, options: [.autoreverse, .repeat], animations: {
            self.containerView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        }) { _ in
            self.containerView.transform = .identity
        }
        
        // Stop animation after 2 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.containerView.layer.removeAllAnimations()
            self.containerView.transform = .identity
        }
    }
    
    /// Animate the timer for race completion
    func animateRaceCompletion() {
        // Change border color to success
        UIView.animate(withDuration: 0.5) {
            self.containerView.layer.borderColor = UIColor.dragonSuccess.cgColor
        }
        
        // Celebration animation
        UIView.animate(withDuration: 0.6, delay: 0, usingSpringWithDamping: 0.6, initialSpringVelocity: 0.8, options: [], animations: {
            self.containerView.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.3) {
                self.containerView.transform = .identity
            }
        }
    }
}
