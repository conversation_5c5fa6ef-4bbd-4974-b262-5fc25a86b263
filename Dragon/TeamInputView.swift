//
//  TeamInputView.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

protocol TeamInputViewDelegate: AnyObject {
    func teamInputViewDidChange(_ teamInputView: TeamInputView)
    func teamInputViewDidRequestRemoval(_ teamInputView: TeamInputView)
}

class TeamInputView: UIView {
    
    // MARK: - UI Components
    
    private let containerView = UIView()
    private let teamTextField = UITextField()
    private let removeButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    weak var delegate: TeamInputViewDelegate?
    
    var teamName: String {
        get {
            return teamTextField.text ?? ""
        }
        set {
            teamTextField.text = newValue
        }
    }
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        backgroundColor = .clear
        
        // Container view
        containerView.backgroundColor = UIColor.dragonCardBackground
        containerView.layer.cornerRadius = 8
        containerView.layer.borderWidth = 1
        containerView.layer.borderColor = UIColor.dragonBorder.cgColor
        addSubview(containerView)
        
        // Team text field
        teamTextField.placeholder = "Enter team name"
        teamTextField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        teamTextField.textColor = UIColor.dragonTextPrimary
        teamTextField.backgroundColor = .clear
        teamTextField.borderStyle = .none
        teamTextField.attributedPlaceholder = NSAttributedString(
            string: "Enter team name",
            attributes: [.foregroundColor: UIColor.dragonTextTertiary]
        )
        teamTextField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        containerView.addSubview(teamTextField)
        
        // Remove button
        removeButton.setImage(UIImage(systemName: "minus.circle.fill"), for: .normal)
        removeButton.tintColor = UIColor.dragonError
        removeButton.addTarget(self, action: #selector(removePressed), for: .touchUpInside)
        containerView.addSubview(removeButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(48)
        }
        
        teamTextField.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.trailing.equalTo(removeButton.snp.leading).offset(-12)
        }
        
        removeButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
    }
    
    // MARK: - Actions
    
    @objc private func textFieldDidChange() {
        delegate?.teamInputViewDidChange(self)
    }
    
    @objc private func removePressed() {
        delegate?.teamInputViewDidRequestRemoval(self)
    }
    
    // MARK: - Public Methods
    
    override func becomeFirstResponder() -> Bool {
        return teamTextField.becomeFirstResponder()
    }
    
    override func resignFirstResponder() -> Bool {
        return teamTextField.resignFirstResponder()
    }
}
