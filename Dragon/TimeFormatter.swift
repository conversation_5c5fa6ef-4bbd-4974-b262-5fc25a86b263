//
//  TimeFormatter.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Utility class for formatting time intervals in race contexts
class TimeFormatter {
    
    // MARK: - Static Methods
    
    /// Format a time interval into a readable string
    /// - Parameter timeInterval: Time in seconds
    /// - Returns: Formatted string (e.g., "1:23.45", "23.45", "1:02:34.56")
    static func formatTime(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        let milliseconds = Int((timeInterval.truncatingRemainder(dividingBy: 1)) * 100)
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d.%02d", hours, minutes, seconds, milliseconds)
        } else if minutes > 0 {
            return String(format: "%d:%02d.%02d", minutes, seconds, milliseconds)
        } else {
            return String(format: "%d.%02d", seconds, milliseconds)
        }
    }
    
    /// Format a time interval for display in timer (with more precision)
    /// - Parameter timeInterval: Time in seconds
    /// - Returns: Formatted string with milliseconds (e.g., "1:23.456")
    static func formatTimerTime(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let minutes = totalSeconds / 60
        let seconds = totalSeconds % 60
        let milliseconds = Int((timeInterval.truncatingRemainder(dividingBy: 1)) * 1000)
        
        if minutes > 0 {
            return String(format: "%d:%02d.%03d", minutes, seconds, milliseconds)
        } else {
            return String(format: "%d.%03d", seconds, milliseconds)
        }
    }
    
    /// Format time for race duration display
    /// - Parameter timeInterval: Time in seconds
    /// - Returns: Formatted string for race duration
    static func formatRaceDuration(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%dh %dm %ds", hours, minutes, seconds)
        } else if minutes > 0 {
            return String(format: "%dm %ds", minutes, seconds)
        } else {
            return String(format: "%ds", seconds)
        }
    }
    
    /// Parse a time string back to TimeInterval
    /// - Parameter timeString: Formatted time string
    /// - Returns: TimeInterval or nil if parsing fails
    static func parseTime(_ timeString: String) -> TimeInterval? {
        let components = timeString.components(separatedBy: CharacterSet(charactersIn: ":."))
        
        guard !components.isEmpty else { return nil }
        
        var timeInterval: TimeInterval = 0
        
        // Handle different formats
        if components.count == 2 {
            // Format: "seconds.milliseconds"
            if let seconds = Double(components[0]),
               let milliseconds = Double(components[1]) {
                timeInterval = seconds + (milliseconds / 100)
            }
        } else if components.count == 3 {
            // Format: "minutes:seconds.milliseconds"
            if let minutes = Double(components[0]),
               let seconds = Double(components[1]),
               let milliseconds = Double(components[2]) {
                timeInterval = (minutes * 60) + seconds + (milliseconds / 100)
            }
        } else if components.count == 4 {
            // Format: "hours:minutes:seconds.milliseconds"
            if let hours = Double(components[0]),
               let minutes = Double(components[1]),
               let seconds = Double(components[2]),
               let milliseconds = Double(components[3]) {
                timeInterval = (hours * 3600) + (minutes * 60) + seconds + (milliseconds / 100)
            }
        }
        
        return timeInterval > 0 ? timeInterval : nil
    }
    
    /// Get current timestamp string for race records
    /// - Returns: Formatted timestamp string
    static func currentTimestamp() -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: Date())
    }
    
    /// Format date for race record display
    /// - Parameter date: Date to format
    /// - Returns: Formatted date string
    static func formatRaceDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}
