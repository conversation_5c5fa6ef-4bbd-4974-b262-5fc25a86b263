//
//  PerformanceTrendsViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit
import AAInfographics

class PerformanceTrendsViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    private let subtitleLabel = UILabel()
    
    // Chart container
    private let chartCard = UIView()
    private let chartTitleLabel = UILabel()
    private let chartView = AAChartView()
    
    // Team selector
    private let teamSelectorCard = UIView()
    private let teamSelectorLabel = UILabel()
    private let teamPickerView = UIPickerView()
    
    // Statistics card
    private let statsCard = UIView()
    private let statsLabel = UILabel()
    private let statsStackView = UIStackView()
    
    // MARK: - Properties
    
    private let races: [RaceRecord]
    private var availableTeams: [String] = []
    private var selectedTeam: String?
    private var teamStatistics: [String: TeamStatistics] = [:]
    
    // MARK: - Initialization
    
    init(races: [RaceRecord]) {
        self.races = races
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        loadData()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Performance Trends"
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)
        
        // Setup title
        titleLabel.text = "Team Performance Trends"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        subtitleLabel.text = "Track performance improvements over time"
        subtitleLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.dragonTextSecondary
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        contentView.addSubview(subtitleLabel)
        
        setupTeamSelectorCard()
        setupChartCard()
        setupStatsCard()
    }
    
    private func setupTeamSelectorCard() {
        teamSelectorCard.backgroundColor = UIColor.dragonCardBackground
        teamSelectorCard.layer.cornerRadius = 12
        teamSelectorCard.layer.borderWidth = 1
        teamSelectorCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(teamSelectorCard)
        
        teamSelectorLabel.text = "Select Team"
        teamSelectorLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        teamSelectorLabel.textColor = UIColor.dragonTextPrimary
        teamSelectorCard.addSubview(teamSelectorLabel)
        
        teamPickerView.delegate = self
        teamPickerView.dataSource = self
        teamSelectorCard.addSubview(teamPickerView)
    }
    
    private func setupChartCard() {
        chartCard.backgroundColor = UIColor.dragonCardBackground
        chartCard.layer.cornerRadius = 12
        chartCard.layer.borderWidth = 1
        chartCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(chartCard)
        
        chartTitleLabel.text = "Performance Over Time"
        chartTitleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        chartTitleLabel.textColor = UIColor.dragonTextPrimary
        chartCard.addSubview(chartTitleLabel)
        
        chartView.backgroundColor = .clear
        chartCard.addSubview(chartView)
    }
    
    private func setupStatsCard() {
        statsCard.backgroundColor = UIColor.dragonCardBackground
        statsCard.layer.cornerRadius = 12
        statsCard.layer.borderWidth = 1
        statsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(statsCard)
        
        statsLabel.text = "Performance Statistics"
        statsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        statsLabel.textColor = UIColor.dragonTextPrimary
        statsCard.addSubview(statsLabel)
        
        statsStackView.axis = .vertical
        statsStackView.spacing = 12
        statsStackView.distribution = .fillEqually
        statsCard.addSubview(statsStackView)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        teamSelectorCard.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(160)
        }
        
        teamSelectorLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        teamPickerView.snp.makeConstraints { make in
            make.top.equalTo(teamSelectorLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        chartCard.snp.makeConstraints { make in
            make.top.equalTo(teamSelectorCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(300)
        }
        
        chartTitleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        chartView.snp.makeConstraints { make in
            make.top.equalTo(chartTitleLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        statsCard.snp.makeConstraints { make in
            make.top.equalTo(chartCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(250) // 增加高度避免拥挤
            make.bottom.equalToSuperview().offset(-20)
        }
        
        statsLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }
        
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(statsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.lessThanOrEqualToSuperview().offset(-16)
        }
    }
    
    // MARK: - Data Loading
    
    private func loadData() {
        // Calculate team statistics
        for race in races {
            for team in race.teams {
                if teamStatistics[team.name] == nil {
                    teamStatistics[team.name] = TeamStatistics(name: team.name)
                }
                teamStatistics[team.name]?.addRace(team: team)
            }
        }
        
        // Get available teams sorted by number of races
        availableTeams = teamStatistics.keys.sorted { teamName1, teamName2 in
            let races1 = teamStatistics[teamName1]?.totalRaces ?? 0
            let races2 = teamStatistics[teamName2]?.totalRaces ?? 0
            return races1 > races2
        }
        
        teamPickerView.reloadAllComponents()
        
        // Select first team if available
        if !availableTeams.isEmpty {
            selectedTeam = availableTeams[0]
            updateChartAndStats()
        }
    }
    
    private func updateChartAndStats() {
        guard let teamName = selectedTeam,
              let stats = teamStatistics[teamName] else {
            return
        }
        
        updateChart(for: teamName)
        updateStats(for: stats)
    }
    
    private func updateChart(for teamName: String) {
        // Get team's race data over time
        var timeData: [Double] = []
        var categories: [String] = []
        var raceIndex = 1

        for race in races.sorted(by: { $0.createdAt < $1.createdAt }) {
            if let team = race.teams.first(where: { $0.name == teamName }),
               let finishTime = team.finishTime {
                timeData.append(finishTime)
                categories.append("Race \(raceIndex)")
                raceIndex += 1
            }
        }

        // Create chart model
        let chartModel = AAChartModel()
            .chartType(.line)
            .title("Performance Trend - \(teamName)")
            .subtitle("Race times over time")
            .backgroundColor("#1C1C1E")
            .dataLabelsEnabled(true)
            .yAxisTitle("Time (seconds)")
            .categories(categories)
            .series([
                AASeriesElement()
                    .name(teamName)
                    .data(timeData)
                    .color("#007AFF")
                    .lineWidth(3)
                    .marker(AAMarker()
                        .radius(6)
                        .fillColor("#007AFF")
                        .lineColor("#FFFFFF")
                        .lineWidth(2))
            ])
            .yAxisGridLineWidth(0.5)
            
            .xAxisGridLineWidth(0.5)
            
            .legendEnabled(false)

        chartView.aa_drawChartWithChartModel(chartModel)
    }
    
    private func updateStats(for stats: TeamStatistics) {
        // Clear existing stats
        statsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add statistics with fixed heights
        let row1 = createStatRow("Total Races:", "\(stats.totalRaces)")
        row1.snp.makeConstraints { make in
            make.height.equalTo(28)
        }
        statsStackView.addArrangedSubview(row1)

        let row2 = createStatRow("Completion Rate:", String(format: "%.1f%%", stats.completionRate * 100))
        row2.snp.makeConstraints { make in
            make.height.equalTo(28)
        }
        statsStackView.addArrangedSubview(row2)

        if let avgTime = stats.averageTime {
            let row3 = createStatRow("Average Time:", TimeFormatter.formatTime(avgTime))
            row3.snp.makeConstraints { make in
                make.height.equalTo(28)
            }
            statsStackView.addArrangedSubview(row3)
        }

        if let bestTime = stats.bestTime {
            let row4 = createStatRow("Best Time:", TimeFormatter.formatTime(bestTime))
            row4.snp.makeConstraints { make in
                make.height.equalTo(28)
            }
            statsStackView.addArrangedSubview(row4)
        }

        if let avgRank = stats.averageRank {
            let row5 = createStatRow("Average Rank:", String(format: "%.1f", avgRank))
            row5.snp.makeConstraints { make in
                make.height.equalTo(28)
            }
            statsStackView.addArrangedSubview(row5)
        }

        let row6 = createStatRow("Win Rate:", String(format: "%.1f%%", stats.winRate * 100))
        row6.snp.makeConstraints { make in
            make.height.equalTo(28)
        }
        statsStackView.addArrangedSubview(row6)

        let row7 = createStatRow("Podium Rate:", String(format: "%.1f%%", stats.podiumRate * 100))
        row7.snp.makeConstraints { make in
            make.height.equalTo(28)
        }
        statsStackView.addArrangedSubview(row7)

        let trendRow = createTrendRow("Trend:", stats.performanceTrend)
        trendRow.snp.makeConstraints { make in
            make.height.equalTo(28)
        }
        statsStackView.addArrangedSubview(trendRow)
    }
    
    private func createStatRow(_ label: String, _ value: String) -> UIView {
        let container = UIView()

        let labelView = UILabel()
        labelView.text = label
        labelView.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        labelView.textColor = UIColor.dragonTextSecondary
        labelView.numberOfLines = 1
        container.addSubview(labelView)

        let valueView = UILabel()
        valueView.text = value
        valueView.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        valueView.textColor = UIColor.dragonTextPrimary
        valueView.textAlignment = .right
        valueView.numberOfLines = 1
        container.addSubview(valueView)

        labelView.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.height.equalTo(20)
        }

        valueView.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
            make.height.equalTo(20)
        }

        return container
    }
    
    private func createTrendRow(_ label: String, _ trend: PerformanceTrend) -> UIView {
        let container = UIView()

        let labelView = UILabel()
        labelView.text = label
        labelView.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        labelView.textColor = UIColor.dragonTextSecondary
        labelView.numberOfLines = 1
        container.addSubview(labelView)

        let valueView = UILabel()
        valueView.text = trend.displayName
        valueView.font = UIFont.systemFont(ofSize: 14, weight: .semibold)
        valueView.textColor = trend.color
        valueView.textAlignment = .right
        valueView.numberOfLines = 1
        container.addSubview(valueView)

        labelView.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.height.equalTo(20)
        }

        valueView.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview()
            make.height.equalTo(20)
        }

        return container
    }
}

// MARK: - UIPickerViewDataSource & UIPickerViewDelegate

extension PerformanceTrendsViewController: UIPickerViewDataSource, UIPickerViewDelegate {
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        return 1
    }
    
    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        return availableTeams.count
    }
    
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        guard row < availableTeams.count else { return nil }
        let teamName = availableTeams[row]
        let raceCount = teamStatistics[teamName]?.totalRaces ?? 0
        return "\(teamName) (\(raceCount) races)"
    }
    
    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        guard row < availableTeams.count else { return }
        selectedTeam = availableTeams[row]
        updateChartAndStats()
    }
}
