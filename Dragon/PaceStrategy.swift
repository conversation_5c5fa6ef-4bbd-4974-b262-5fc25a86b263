//
//  PaceStrategy.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Represents a complete pace strategy for a dragon boat race
class PaceStrategy: NSObject, Codable {
    
    // MARK: - Properties
    
    /// Unique identifier for the strategy
    let id: UUID
    
    /// Name of the strategy
    var name: String
    
    /// Total race distance in meters
    var raceDistance: Double
    
    /// Target total race time in seconds
    var targetTime: TimeInterval
    
    /// Average stroke distance in meters per stroke
    var averageStrokeDistance: Double
    
    /// List of rhythm phases that make up this strategy
    var phases: [RhythmPhase]
    
    /// Strategy type/template this is based on
    var strategyType: StrategyType
    
    /// Date when strategy was created
    let createdAt: Date
    
    /// Date when strategy was last modified
    var modifiedAt: Date
    
    /// Whether this is a custom user-created strategy
    var isCustom: Bool
    
    /// Notes or description for this strategy
    var notes: String?
    
    // MARK: - Computed Properties
    
    /// Total duration of all phases
    var totalDuration: TimeInterval {
        return phases.reduce(0) { $0 + $1.duration }
    }
    
    /// Total distance of all phases
    var totalPhaseDistance: Double {
        return phases.reduce(0) { $0 + $1.distance }
    }
    
    /// Average speed for the entire race (m/s)
    var averageSpeed: Double {
        return raceDistance / targetTime
    }
    
    /// Average speed in km/h
    var averageSpeedKmh: Double {
        return averageSpeed * 3.6
    }
    
    /// Total number of strokes for the race
    var totalStrokes: Int {
        return phases.reduce(0) { $0 + $1.totalStrokes }
    }
    
    /// Average stroke rate for the entire race
    var averageStrokeRate: Double {
        let totalStrokeTime = phases.reduce(0.0) { $0 + ($1.strokeRate * $1.duration) }
        return totalStrokeTime / totalDuration
    }
    
    /// Whether the strategy is valid (phases match race distance and time)
    var isValid: Bool {
        let distanceDiff = abs(totalPhaseDistance - raceDistance)
        let timeDiff = abs(totalDuration - targetTime)
        return distanceDiff < 1.0 && timeDiff < 1.0 // Allow 1m and 1s tolerance
    }
    
    // MARK: - Initialization
    
    init(name: String,
         raceDistance: Double,
         targetTime: TimeInterval,
         averageStrokeDistance: Double = 2.5,
         strategyType: StrategyType = .custom,
         isCustom: Bool = true) {
        
        self.id = UUID()
        self.name = name
        self.raceDistance = raceDistance
        self.targetTime = targetTime
        self.averageStrokeDistance = averageStrokeDistance
        self.phases = []
        self.strategyType = strategyType
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.isCustom = isCustom
        super.init()
    }
    
    // MARK: - Phase Management
    
    /// Add a phase to the strategy
    /// - Parameter phase: Phase to add
    func addPhase(_ phase: RhythmPhase) {
        phases.append(phase)
        updatePhasePercentages()
        modifiedAt = Date()
    }
    
    /// Remove a phase from the strategy
    /// - Parameter phase: Phase to remove
    func removePhase(_ phase: RhythmPhase) {
        phases.removeAll { $0.id == phase.id }
        updatePhasePercentages()
        modifiedAt = Date()
    }
    
    /// Update time percentages for all phases
    private func updatePhasePercentages() {
        let total = totalDuration
        guard total > 0 else { return }
        
        for phase in phases {
            phase.timePercentage = phase.duration / total
        }
    }
    
    /// Auto-balance phases to match target time and distance
    func autoBalance() {
        guard !phases.isEmpty else { return }
        
        let timeRatio = targetTime / totalDuration
        let distanceRatio = raceDistance / totalPhaseDistance
        
        for phase in phases {
            phase.duration *= timeRatio
            phase.distance *= distanceRatio
            phase.updateDurationFromDistance()
        }
        
        updatePhasePercentages()
        modifiedAt = Date()
    }
    
    // MARK: - Strategy Analysis
    
    /// Get performance prediction for a team based on historical data
    /// - Parameter team: Team to analyze
    /// - Returns: Predicted finish time
    func predictPerformance(for team: Team) -> TimeInterval? {
        // This would use historical data to predict performance
        // For now, return the target time as baseline
        return targetTime
    }
    
    /// Get substitution recommendations
    /// - Returns: Array of recommended substitution points
    func getSubstitutionRecommendations() -> [(distance: Double, timing: String)] {
        var recommendations: [(distance: Double, timing: String)] = []
        var currentDistance: Double = 0
        
        for phase in phases {
            if phase.allowsSubstitution, let timing = phase.substitutionTiming {
                let substitutionDistance = currentDistance + (phase.distance * timing)
                let timingDescription = "\(phase.name) - \(Int(timing * 100))%"
                recommendations.append((substitutionDistance, timingDescription))
            }
            currentDistance += phase.distance
        }
        
        return recommendations
    }
    
    /// Export strategy as text summary
    func exportSummary() -> String {
        var summary = "🐉 \(name)\n"
        summary += "📏 Distance: \(Int(raceDistance))m\n"
        summary += "⏱️ Target Time: \(TimeFormatter.formatTime(targetTime))\n"
        summary += "🚣 Average Speed: \(String(format: "%.1f", averageSpeedKmh)) km/h\n"
        summary += "📊 Total Strokes: \(totalStrokes)\n"
        summary += "🎯 Avg Stroke Rate: \(Int(averageStrokeRate)) SPM\n\n"
        
        summary += "📋 Phase Breakdown:\n"
        for (index, phase) in phases.enumerated() {
            summary += "\(index + 1). \(phase.getDescription())\n"
        }
        
        let substitutions = getSubstitutionRecommendations()
        if !substitutions.isEmpty {
            summary += "\n🔄 Substitution Points:\n"
            for sub in substitutions {
                summary += "• \(Int(sub.distance))m - \(sub.timing)\n"
            }
        }
        
        if let notes = notes, !notes.isEmpty {
            summary += "\n📝 Notes: \(notes)\n"
        }
        
        return summary
    }
    
    // MARK: - Codable
    
    enum CodingKeys: String, CodingKey {
        case id, name, raceDistance, targetTime, averageStrokeDistance
        case phases, strategyType, createdAt, modifiedAt, isCustom, notes
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(UUID.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        raceDistance = try container.decode(Double.self, forKey: .raceDistance)
        targetTime = try container.decode(TimeInterval.self, forKey: .targetTime)
        averageStrokeDistance = try container.decode(Double.self, forKey: .averageStrokeDistance)
        phases = try container.decode([RhythmPhase].self, forKey: .phases)
        strategyType = try container.decode(StrategyType.self, forKey: .strategyType)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        modifiedAt = try container.decode(Date.self, forKey: .modifiedAt)
        isCustom = try container.decode(Bool.self, forKey: .isCustom)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        super.init()
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(raceDistance, forKey: .raceDistance)
        try container.encode(targetTime, forKey: .targetTime)
        try container.encode(averageStrokeDistance, forKey: .averageStrokeDistance)
        try container.encode(phases, forKey: .phases)
        try container.encode(strategyType, forKey: .strategyType)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(modifiedAt, forKey: .modifiedAt)
        try container.encode(isCustom, forKey: .isCustom)
        try container.encodeIfPresent(notes, forKey: .notes)
    }
}

// MARK: - Strategy Type Enum

enum StrategyType: String, Codable, CaseIterable {
    case evenPace = "even_pace"
    case startBurst = "start_burst"
    case finishSprint = "finish_sprint"
    case negative = "negative"
    case positive = "positive"
    case custom = "custom"
    
    var displayName: String {
        switch self {
        case .evenPace:
            return "Even Pace"
        case .startBurst:
            return "Start Burst"
        case .finishSprint:
            return "Finish Sprint"
        case .negative:
            return "Negative Split"
        case .positive:
            return "Positive Split"
        case .custom:
            return "Custom"
        }
    }
    
    var description: String {
        switch self {
        case .evenPace:
            return "Maintain consistent pace throughout"
        case .startBurst:
            return "Fast start, settle into rhythm"
        case .finishSprint:
            return "Build up to strong finish"
        case .negative:
            return "Start conservative, finish fast"
        case .positive:
            return "Start fast, maintain as long as possible"
        case .custom:
            return "User-defined strategy"
        }
    }
}
