//
//  RaceTimerViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class RaceTimerViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let raceNameLabel = UILabel()
    private let timerDisplayView = TimerDisplayView()
    private let statusLabel = UILabel()
    
    private let teamsLabel = UILabel()
    private let teamsStackView = UIStackView()
    
    private let endRaceButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private let race: RaceRecord
    private let raceManager = RaceManager.shared
    private var teamTimerViews: [TeamTimerView] = []
    
    // MARK: - Initialization
    
    init(race: RaceRecord) {
        self.race = race
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupTeamViews()
        setupRaceManager()
        updateUI()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        // If race is completed, navigate back to main
        if race.status == .completed {
            navigationController?.popToRootViewController(animated: false)
        }
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Race Timer"
        
        // Navigation
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "chevron.left"),
            style: .plain,
            target: self,
            action: #selector(backPressed)
        )
        
        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        scrollView.delaysContentTouches = false
        scrollView.canCancelContentTouches = false
        view.addSubview(scrollView)
        
        contentView.backgroundColor = .clear
        contentView.isUserInteractionEnabled = true
        scrollView.addSubview(contentView)
        
        // Setup race name
        raceNameLabel.text = race.name
        raceNameLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        raceNameLabel.textColor = UIColor.dragonTextPrimary
        raceNameLabel.textAlignment = .center
        raceNameLabel.numberOfLines = 0
        contentView.addSubview(raceNameLabel)
        
        // Setup timer display
        contentView.addSubview(timerDisplayView)
        
        // Setup status label
        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        statusLabel.textColor = UIColor.dragonTextSecondary
        statusLabel.textAlignment = .center
        statusLabel.numberOfLines = 0
        contentView.addSubview(statusLabel)
        
        // Setup teams section
        teamsLabel.text = "Teams"
        teamsLabel.font = UIFont.systemFont(ofSize: 20, weight: .semibold)
        teamsLabel.textColor = UIColor.dragonTextPrimary
        contentView.addSubview(teamsLabel)
        
        teamsStackView.axis = .vertical
        teamsStackView.spacing = 12
        teamsStackView.distribution = .fill
        teamsStackView.isUserInteractionEnabled = true
        contentView.addSubview(teamsStackView)
        
        // Setup end race button
        endRaceButton.setTitle("End Race", for: .normal)
        endRaceButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        endRaceButton.setTitleColor(.white, for: .normal)
        endRaceButton.backgroundColor = UIColor.dragonError
        endRaceButton.layer.cornerRadius = 12
        endRaceButton.addTarget(self, action: #selector(endRacePressed), for: .touchUpInside)
        contentView.addSubview(endRaceButton)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        raceNameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        timerDisplayView.snp.makeConstraints { make in
            make.top.equalTo(raceNameLabel.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(120)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(timerDisplayView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        teamsLabel.snp.makeConstraints { make in
            make.top.equalTo(statusLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        teamsStackView.snp.makeConstraints { make in
            make.top.equalTo(teamsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        endRaceButton.snp.makeConstraints { make in
            make.top.equalTo(teamsStackView.snp.bottom).offset(40)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupTeamViews() {
        for team in race.teams {
            let teamTimerView = TeamTimerView(team: team)
            teamTimerView.delegate = self
            teamTimerViews.append(teamTimerView)
            teamsStackView.addArrangedSubview(teamTimerView)
        }
    }
    
    private func setupRaceManager() {
        raceManager.delegate = self
        raceManager.setCurrentRace(race)
    }
    
    // MARK: - Actions
    
    @objc private func backPressed() {
        if race.status == .inProgress {
            let alert = UIAlertController(
                title: "Race in Progress",
                message: "The race is still in progress. Are you sure you want to go back?",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Go Back", style: .default) { _ in
                self.navigationController?.popViewController(animated: true)
            })
            
            present(alert, animated: true)
        } else {
            navigationController?.popViewController(animated: true)
        }
    }
    
    @objc private func endRacePressed() {
        let alert = UIAlertController(
            title: "End Race",
            message: "Are you sure you want to end the race? This will finalize the results.",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "End Race", style: .destructive) { _ in
            self.raceManager.endCurrentRace()
        })
        
        present(alert, animated: true)
    }
    
    // MARK: - UI Updates
    
    private func updateUI() {
        // Update timer display
        if let elapsedTime = raceManager.currentRaceElapsedTime {
            timerDisplayView.updateTime(elapsedTime)
        }
        
        // Update status
        let finishedCount = race.finishedTeamsCount
        let totalCount = race.teams.count
        
        if race.status == .completed {
            statusLabel.text = "Race Completed! 🏁"
            statusLabel.textColor = UIColor.dragonSuccess
            endRaceButton.isHidden = true
        } else {
            statusLabel.text = "\(finishedCount) of \(totalCount) teams finished"
            statusLabel.textColor = UIColor.dragonTextSecondary
            endRaceButton.isHidden = false
        }
        
        // Update team views
        for teamTimerView in teamTimerViews {
            teamTimerView.updateUI()
        }
    }
}

// MARK: - RaceManagerDelegate

extension RaceTimerViewController: RaceManagerDelegate {
    func raceManager(_ manager: RaceManager, didUpdateRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()
        }
    }

    func raceManager(_ manager: RaceManager, didFinishTeam team: Team, in race: RaceRecord) {
        DispatchQueue.main.async {
            // Find and update the specific team view
            if let teamView = self.teamTimerViews.first(where: { $0.team == team }) {
                teamView.updateUI()
            }
            self.updateUI()
        }
    }

    func raceManager(_ manager: RaceManager, didCompleteRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()

            // Show completion alert
            let alert = UIAlertController(
                title: "Race Completed! 🏁",
                message: "All teams have finished. View the results?",
                preferredStyle: .alert
            )

            alert.addAction(UIAlertAction(title: "Later", style: .cancel))
            alert.addAction(UIAlertAction(title: "View Results", style: .default) { _ in
                let resultsVC = RaceResultsViewController(race: race)
                self.navigationController?.pushViewController(resultsVC, animated: true)
            })

            self.present(alert, animated: true)
        }
    }
}

// MARK: - TeamTimerViewDelegate

extension RaceTimerViewController: TeamTimerViewDelegate {
    func teamTimerView(_ teamTimerView: TeamTimerView, didFinishTeam team: Team) {
        print("TeamTimerViewDelegate called for team: \(team.name)")
        
        // 确保在主线程上执行 UI 操作
        DispatchQueue.main.async {
            // 添加视觉反馈
            if let index = self.teamTimerViews.firstIndex(where: { $0.team.id == team.id }) {
                self.teamTimerViews[index].animateFinish()
            }
            
            // 完成团队
            let success = self.raceManager.finishTeam(team)
            print("RaceManager.finishTeam returned: \(success)")
            
            if success {
                // 立即更新 UI 以提供及时反馈
                self.updateUI()
            }
        }
    }
}
