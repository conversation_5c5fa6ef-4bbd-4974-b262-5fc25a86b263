//
//  RaceTimerViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class RaceTimerViewController: UIViewController {

    // MARK: - UI Components

    private let tableView = UITableView(frame: .zero, style: .grouped)

    // MARK: - Properties

    private let race: RaceRecord
    private let raceManager = RaceManager.shared
    
    // MARK: - Initialization
    
    init(race: RaceRecord) {
        self.race = race
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupRaceManager()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        // If race is completed, navigate back to main
        if race.status == .completed {
            navigationController?.popToRootViewController(animated: false)
        }
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Race Timer"

        // Navigation
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "chevron.left"),
            style: .plain,
            target: self,
            action: #selector(backPressed)
        )

        // Setup table view
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.showsVerticalScrollIndicator = false
        tableView.delaysContentTouches = false
        tableView.canCancelContentTouches = false

        // Register cells
        tableView.register(RaceHeaderCell.self, forCellReuseIdentifier: "RaceHeaderCell")
        tableView.register(TimerDisplayCell.self, forCellReuseIdentifier: "TimerDisplayCell")
        tableView.register(StatusCell.self, forCellReuseIdentifier: "StatusCell")
        tableView.register(TeamTimerCell.self, forCellReuseIdentifier: "TeamTimerCell")

        view.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func setupRaceManager() {
        raceManager.delegate = self
        raceManager.setCurrentRace(race)
    }
    
    // MARK: - Actions
    
    @objc private func backPressed() {
        if race.status == .inProgress {
            let alert = UIAlertController(
                title: "Race in Progress",
                message: "The race is still in progress. Are you sure you want to go back?",
                preferredStyle: .alert
            )
            
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Go Back", style: .default) { _ in
                self.navigationController?.popViewController(animated: true)
            })
            
            present(alert, animated: true)
        } else {
            navigationController?.popViewController(animated: true)
        }
    }
    
    @objc private func endRacePressed() {
        let alert = UIAlertController(
            title: "End Race",
            message: "Are you sure you want to end the race? This will finalize the results.",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "End Race", style: .destructive) { _ in
            self.raceManager.endCurrentRace()
        })
        
        present(alert, animated: true)
    }
    
    // MARK: - UI Updates

    private func updateUI() {
        DispatchQueue.main.async {
            self.tableView.reloadData()
        }
    }
}

// MARK: - RaceManagerDelegate

extension RaceTimerViewController: RaceManagerDelegate {
    func raceManager(_ manager: RaceManager, didUpdateRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()
        }
    }

    func raceManager(_ manager: RaceManager, didFinishTeam team: Team, in race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()
        }
    }

    func raceManager(_ manager: RaceManager, didCompleteRace race: RaceRecord) {
        DispatchQueue.main.async {
            self.updateUI()

            // Show completion alert
            let alert = UIAlertController(
                title: "Race Completed! 🏁",
                message: "All teams have finished. View the results?",
                preferredStyle: .alert
            )

            alert.addAction(UIAlertAction(title: "Later", style: .cancel))
            alert.addAction(UIAlertAction(title: "View Results", style: .default) { _ in
                let resultsVC = RaceResultsViewController(race: race)
                self.navigationController?.pushViewController(resultsVC, animated: true)
            })

            self.present(alert, animated: true)
        }
    }
}

// MARK: - TeamTimerViewDelegate

extension RaceTimerViewController: TeamTimerViewDelegate {
    func teamTimerView(_ teamTimerView: TeamTimerView, didFinishTeam team: Team) {
        print("TeamTimerViewDelegate called for team: \(team.name)")

        // 确保在主线程上执行 UI 操作
        DispatchQueue.main.async {
            // 完成团队
            let success = self.raceManager.finishTeam(team)
            print("RaceManager.finishTeam returned: \(success)")

            if success {
                // 立即更新 UI 以提供及时反馈
                self.updateUI()
            }
        }
    }
}

// MARK: - UITableViewDataSource

extension RaceTimerViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 4 // Header, Timer, Status, Teams, End Button
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch section {
        case 0: return 1 // Race Header
        case 1: return 1 // Timer Display
        case 2: return 1 // Status
        case 3: return race.teams.count // Teams
        default: return 0
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        switch indexPath.section {
        case 0: // Race Header
            let cell = tableView.dequeueReusableCell(withIdentifier: "RaceHeaderCell", for: indexPath) as! RaceHeaderCell
            cell.configure(with: race.name)
            return cell

        case 1: // Timer Display
            let cell = tableView.dequeueReusableCell(withIdentifier: "TimerDisplayCell", for: indexPath) as! TimerDisplayCell
            if let elapsedTime = raceManager.currentRaceElapsedTime {
                cell.configure(with: elapsedTime)
            }
            return cell

        case 2: // Status
            let cell = tableView.dequeueReusableCell(withIdentifier: "StatusCell", for: indexPath) as! StatusCell
            let finishedCount = race.finishedTeamsCount
            let totalCount = race.teams.count
            cell.configure(finishedCount: finishedCount, totalCount: totalCount, isCompleted: race.status == .completed)
            return cell

        case 3: // Teams
            let cell = tableView.dequeueReusableCell(withIdentifier: "TeamTimerCell", for: indexPath) as! TeamTimerCell
            let team = race.teams[indexPath.row]
            cell.delegate = self
            cell.configure(with: team)
            return cell

        default:
            return UITableViewCell()
        }
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        switch section {
        case 3: return "Teams"
        default: return nil
        }
    }
}

// MARK: - UITableViewDelegate

extension RaceTimerViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch indexPath.section {
        case 0: return 80  // Race Header
        case 1: return 140 // Timer Display
        case 2: return 60  // Status
        case 3: return 90  // Teams
        default: return 80
        }
    }

    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        if section == 3 { // After teams section
            let footerView = UIView()
            footerView.backgroundColor = .clear

            let endRaceButton = UIButton(type: .system)
            endRaceButton.setTitle("End Race", for: .normal)
            endRaceButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
            endRaceButton.setTitleColor(.white, for: .normal)
            endRaceButton.backgroundColor = UIColor.dragonError
            endRaceButton.layer.cornerRadius = 12
            endRaceButton.addTarget(self, action: #selector(endRacePressed), for: .touchUpInside)
            endRaceButton.isHidden = race.status == .completed

            footerView.addSubview(endRaceButton)

            endRaceButton.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(20)
                make.leading.trailing.equalToSuperview().inset(20)
                make.height.equalTo(56)
                make.bottom.equalToSuperview().offset(-20)
            }

            return footerView
        }
        return nil
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return section == 3 ? 100 : 0
    }
}

// MARK: - Custom Cells

class RaceHeaderCell: UITableViewCell {
    private let raceNameLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        raceNameLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        raceNameLabel.textColor = UIColor.dragonTextPrimary
        raceNameLabel.textAlignment = .center
        raceNameLabel.numberOfLines = 0
        contentView.addSubview(raceNameLabel)

        raceNameLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }
    }

    func configure(with raceName: String) {
        raceNameLabel.text = raceName
    }
}

class TimerDisplayCell: UITableViewCell {
    private let timerDisplayView = TimerDisplayView()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(timerDisplayView)

        timerDisplayView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 10, left: 20, bottom: 10, right: 20))
        }
    }

    func configure(with elapsedTime: TimeInterval) {
        timerDisplayView.updateTime(elapsedTime)
    }
}

class StatusCell: UITableViewCell {
    private let statusLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        statusLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        statusLabel.textAlignment = .center
        statusLabel.numberOfLines = 0
        contentView.addSubview(statusLabel)

        statusLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(20)
        }
    }

    func configure(finishedCount: Int, totalCount: Int, isCompleted: Bool) {
        if isCompleted {
            statusLabel.text = "Race Completed! 🏁"
            statusLabel.textColor = UIColor.dragonSuccess
        } else {
            statusLabel.text = "\(finishedCount) of \(totalCount) teams finished"
            statusLabel.textColor = UIColor.dragonTextSecondary
        }
    }
}

class TeamTimerCell: UITableViewCell {
    private var teamTimerView: TeamTimerView?
    weak var delegate: TeamTimerViewDelegate?

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
    }

    func configure(with team: Team) {
        // Remove old view if exists
        teamTimerView?.removeFromSuperview()

        // Create new TeamTimerView with the correct team
        let newTeamTimerView = TeamTimerView(team: team)
        newTeamTimerView.delegate = delegate
        contentView.addSubview(newTeamTimerView)

        newTeamTimerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 5, left: 20, bottom: 5, right: 20))
        }

        self.teamTimerView = newTeamTimerView
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        teamTimerView?.removeFromSuperview()
        teamTimerView = nil
    }
}
