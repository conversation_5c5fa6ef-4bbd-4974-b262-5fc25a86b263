//
//  StrokeRateSimulatorViewController.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import SnapKit

class StrokeRateSimulatorViewController: UIViewController {
    
    // MARK: - UI Components
    
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    
    private let titleLabel = UILabel()
    
    // 基础参数卡片
    private let parametersCard = UIView()
    private let parametersLabel = UILabel()
    private let distanceTextField = UITextField()
    private let targetTimeTextField = UITextField()
    private let strokeDistanceTextField = UITextField()
    
    // 阶段配置卡片
    private let phasesCard = UIView()
    private let phasesLabel = UILabel()
    private let phasesStackView = UIStackView()
    private let addPhaseButton = UIButton(type: .system)
    
    // 结果显示卡片
    private let resultsCard = UIView()
    private let resultsLabel = UILabel()
    private let totalStrokesLabel = UILabel()
    private let averageStrokeRateLabel = UILabel()
    private let substitutionTimingsLabel = UILabel()
    
    // 操作按钮
    private let calculateButton = UIButton(type: .system)
    private let saveStrategyButton = UIButton(type: .system)
    
    // MARK: - Properties
    
    private var phaseViews: [PhaseConfigView] = []
    private var currentStrategy: RaceStrategy?
    private let strategyManager = StrategyManager.shared
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        addDefaultPhases()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        view.backgroundColor = UIColor.dragonDarkBackground
        title = "Stroke Rate Simulator"

        // Navigation bar
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "Save",
            style: .done,
            target: self,
            action: #selector(saveStrategyPressed)
        )

        // Setup scroll view
        scrollView.backgroundColor = .clear
        scrollView.showsVerticalScrollIndicator = false
        view.addSubview(scrollView)

        contentView.backgroundColor = .clear
        scrollView.addSubview(contentView)

        // Setup title
        titleLabel.text = "Create Phase-Based Strategy"
        titleLabel.font = UIFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor.dragonTextPrimary
        titleLabel.textAlignment = .center
        contentView.addSubview(titleLabel)
        
        setupParametersCard()
        setupPhasesCard()
        setupResultsCard()
        setupActionButtons()
    }
    
    private func setupParametersCard() {
        parametersCard.backgroundColor = UIColor.dragonCardBackground
        parametersCard.layer.cornerRadius = 12
        parametersCard.layer.borderWidth = 1
        parametersCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(parametersCard)
        
        parametersLabel.text = "Race Parameters"
        parametersLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        parametersLabel.textColor = UIColor.dragonTextPrimary
        parametersCard.addSubview(parametersLabel)

        // Distance input
        distanceTextField.placeholder = "Race Distance (meters)"
        distanceTextField.text = "500"
        distanceTextField.keyboardType = .numberPad
        setupTextField(distanceTextField)
        parametersCard.addSubview(distanceTextField)

        // Target time input
        targetTimeTextField.placeholder = "Target Time (MM:SS)"
        targetTimeTextField.text = "2:00"
        setupTextField(targetTimeTextField)
        parametersCard.addSubview(targetTimeTextField)

        // Stroke distance input
        strokeDistanceTextField.placeholder = "Stroke Distance (meters)"
        strokeDistanceTextField.text = "2.5"
        strokeDistanceTextField.keyboardType = .decimalPad
        setupTextField(strokeDistanceTextField)
        parametersCard.addSubview(strokeDistanceTextField)
        
        // 添加文本变化监听
        [distanceTextField, targetTimeTextField, strokeDistanceTextField].forEach { textField in
            textField.addTarget(self, action: #selector(parametersChanged), for: .editingChanged)
        }
    }
    
    private func setupPhasesCard() {
        phasesCard.backgroundColor = UIColor.dragonCardBackground
        phasesCard.layer.cornerRadius = 12
        phasesCard.layer.borderWidth = 1
        phasesCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(phasesCard)
        
        phasesLabel.text = "Race Phases Configuration"
        phasesLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        phasesLabel.textColor = UIColor.dragonTextPrimary
        phasesCard.addSubview(phasesLabel)

        phasesStackView.axis = .vertical
        phasesStackView.spacing = 16  // Increased spacing
        phasesStackView.distribution = .fill
        phasesCard.addSubview(phasesStackView)

        addPhaseButton.setTitle("+ Add Phase", for: .normal)
        addPhaseButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        addPhaseButton.setTitleColor(UIColor.dragonAccent, for: .normal)
        addPhaseButton.backgroundColor = UIColor.dragonSecondaryBackground
        addPhaseButton.layer.cornerRadius = 8
        addPhaseButton.layer.borderWidth = 1
        addPhaseButton.layer.borderColor = UIColor.dragonAccent.cgColor
        addPhaseButton.addTarget(self, action: #selector(addPhasePressed), for: .touchUpInside)
        phasesCard.addSubview(addPhaseButton)
    }
    
    private func setupResultsCard() {
        resultsCard.backgroundColor = UIColor.dragonCardBackground
        resultsCard.layer.cornerRadius = 12
        resultsCard.layer.borderWidth = 1
        resultsCard.layer.borderColor = UIColor.dragonBorder.cgColor
        contentView.addSubview(resultsCard)
        
        resultsLabel.text = "Strategy Analysis Results"
        resultsLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        resultsLabel.textColor = UIColor.dragonTextPrimary
        resultsCard.addSubview(resultsLabel)

        totalStrokesLabel.text = "Total Strokes: --"
        totalStrokesLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        totalStrokesLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(totalStrokesLabel)

        averageStrokeRateLabel.text = "Average Stroke Rate: -- SPM"
        averageStrokeRateLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        averageStrokeRateLabel.textColor = UIColor.dragonTextSecondary
        resultsCard.addSubview(averageStrokeRateLabel)

        substitutionTimingsLabel.text = "Substitution Timing: --"
        substitutionTimingsLabel.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        substitutionTimingsLabel.textColor = UIColor.dragonTextSecondary
        substitutionTimingsLabel.numberOfLines = 0
        resultsCard.addSubview(substitutionTimingsLabel)
    }
    
    private func setupActionButtons() {
        calculateButton.setTitle("Calculate Strategy", for: .normal)
        calculateButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        calculateButton.setTitleColor(.white, for: .normal)
        calculateButton.backgroundColor = UIColor.dragonAccentSecondary
        calculateButton.layer.cornerRadius = 12
        calculateButton.addTarget(self, action: #selector(calculatePressed), for: .touchUpInside)
        contentView.addSubview(calculateButton)

        saveStrategyButton.setTitle("Save as Strategy", for: .normal)
        saveStrategyButton.titleLabel?.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        saveStrategyButton.setTitleColor(.white, for: .normal)
        saveStrategyButton.backgroundColor = UIColor.dragonAccent
        saveStrategyButton.layer.cornerRadius = 12
        saveStrategyButton.isEnabled = false
        saveStrategyButton.alpha = 0.6
        saveStrategyButton.addTarget(self, action: #selector(saveStrategyPressed), for: .touchUpInside)
        contentView.addSubview(saveStrategyButton)
    }
    
    private func setupTextField(_ textField: UITextField) {
        textField.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        textField.textColor = UIColor.dragonTextPrimary
        textField.backgroundColor = UIColor.dragonSecondaryBackground
        textField.layer.cornerRadius = 8
        textField.layer.borderWidth = 1
        textField.layer.borderColor = UIColor.dragonBorder.cgColor
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        textField.rightViewMode = .always
        
        if let placeholder = textField.placeholder {
            textField.attributedPlaceholder = NSAttributedString(
                string: placeholder,
                attributes: [.foregroundColor: UIColor.dragonTextTertiary]
            )
        }
    }
    
    private func addDefaultPhases() {
        // Add default three phases
        addPhase(type: .start, name: "Start Phase", durationRatio: 0.2, strokeRate: 70)
        addPhase(type: .middle, name: "Middle Phase", durationRatio: 0.6, strokeRate: 65)
        addPhase(type: .sprint, name: "Sprint Phase", durationRatio: 0.2, strokeRate: 75)
    }
    
    private func addPhase(type: PhaseType, name: String, durationRatio: Double, strokeRate: Double) {
        let phaseView = PhaseConfigView(phaseType: type)
        phaseView.configure(name: name, durationRatio: durationRatio, strokeRate: strokeRate)
        phaseView.delegate = self
        
        phaseViews.append(phaseView)
        phasesStackView.addArrangedSubview(phaseView)
        
        updatePhaseConstraints()
    }
    
    private func updatePhaseConstraints() {
        // Update phase view constraints with more space
        for (index, phaseView) in phaseViews.enumerated() {
            phaseView.snp.makeConstraints { make in
                make.height.equalTo(250)  // Increased height for better spacing
            }
        }
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        // Parameters Card
        parametersCard.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        parametersLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        distanceTextField.snp.makeConstraints { make in
            make.top.equalTo(parametersLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        targetTimeTextField.snp.makeConstraints { make in
            make.top.equalTo(distanceTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
        }

        strokeDistanceTextField.snp.makeConstraints { make in
            make.top.equalTo(targetTimeTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Phases Card
        phasesCard.snp.makeConstraints { make in
            make.top.equalTo(parametersCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        phasesLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        phasesStackView.snp.makeConstraints { make in
            make.top.equalTo(phasesLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        addPhaseButton.snp.makeConstraints { make in
            make.top.equalTo(phasesStackView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(48)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Results Card
        resultsCard.snp.makeConstraints { make in
            make.top.equalTo(phasesCard.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(20)
        }

        resultsLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().offset(16)
        }

        totalStrokesLabel.snp.makeConstraints { make in
            make.top.equalTo(resultsLabel.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        averageStrokeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(totalStrokesLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        substitutionTimingsLabel.snp.makeConstraints { make in
            make.top.equalTo(averageStrokeRateLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // Action Buttons
        calculateButton.snp.makeConstraints { make in
            make.top.equalTo(resultsCard.snp.bottom).offset(30)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
        }

        saveStrategyButton.snp.makeConstraints { make in
            make.top.equalTo(calculateButton.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
            make.height.equalTo(56)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Actions

    @objc private func parametersChanged() {
        // 参数变化时重新计算
        calculatePressed()
    }

    @objc private func addPhasePressed() {
        let alert = UIAlertController(title: "Add Phase", message: "Select phase type", preferredStyle: .actionSheet)

        for phaseType in PhaseType.allCases {
            let action = UIAlertAction(title: phaseType.displayName, style: .default) { _ in
                self.addPhase(type: phaseType, name: phaseType.displayName, durationRatio: 0.2, strokeRate: 65)
            }
            alert.addAction(action)
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = addPhaseButton
            popover.sourceRect = addPhaseButton.bounds
        }

        present(alert, animated: true)
    }

    @objc private func calculatePressed() {
        guard let distanceText = distanceTextField.text,
              let distance = Double(distanceText),
              distance > 0 else {
            showAlert(title: "Invalid Distance", message: "Please enter a valid race distance")
            return
        }

        let targetTime = parseTime(targetTimeTextField.text ?? "2:00")
        let strokeDistance = Double(strokeDistanceTextField.text ?? "2.5") ?? 2.5

        // Create strategy
        let strategy = RaceStrategy(
            name: "Custom Strategy",
            strategyType: .custom,
            raceDistance: distance,
            targetTime: targetTime
        )

        // Add phases
        for phaseView in phaseViews {
            let phaseData = phaseView.getPhaseData()
            let phaseDuration = targetTime * phaseData.durationRatio
            let phaseDistance = distance * phaseData.durationRatio

            let phase = RacePhase(
                name: phaseData.name,
                phaseType: phaseData.type,
                duration: phaseDuration,
                distance: phaseDistance,
                targetStrokeRate: phaseData.strokeRate,
                strokeDistance: strokeDistance,
                allowsSubstitution: phaseData.allowsSubstitution
            )

            if phaseData.allowsSubstitution {
                phase.substitutionTiming = phaseDuration * 0.7 // Substitute at 70% of phase
            }

            strategy.phases.append(phase)
        }

        currentStrategy = strategy
        updateResults()

        // Enable save button
        saveStrategyButton.isEnabled = true
        saveStrategyButton.alpha = 1.0
    }

    @objc private func saveStrategyPressed() {
        guard let strategy = currentStrategy else { return }

        let alert = UIAlertController(title: "Save Strategy", message: "Enter strategy name", preferredStyle: .alert)

        alert.addTextField { textField in
            textField.placeholder = "Strategy Name"
            textField.text = "Custom Strategy - \(Int(strategy.raceDistance))m"
        }

        let saveAction = UIAlertAction(title: "Save", style: .default) { _ in
            guard let name = alert.textFields?.first?.text, !name.isEmpty else { return }

            strategy.name = name
            self.strategyManager.saveStrategy(strategy)
            self.strategyManager.setCurrentStrategy(strategy)

            self.showAlert(title: "Save Successful", message: "Strategy saved and set as current strategy") {
                self.navigationController?.popViewController(animated: true)
            }
        }

        alert.addAction(saveAction)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        present(alert, animated: true)
    }

    // MARK: - Helper Methods

    private func updateResults() {
        guard let strategy = currentStrategy else { return }

        totalStrokesLabel.text = "Total Strokes: \(strategy.totalStrokes)"
        averageStrokeRateLabel.text = "Average Stroke Rate: \(Int(strategy.averageStrokeRate)) SPM"

        let timings = strategy.substitutionTimings
        if timings.isEmpty {
            substitutionTimingsLabel.text = "Substitution Timing: None"
        } else {
            let timingStrings = timings.map { TimeFormatter.formatTime($0) }
            substitutionTimingsLabel.text = "Substitution Timing: \(timingStrings.joined(separator: ", "))"
        }

        // Update result colors
        totalStrokesLabel.textColor = UIColor.dragonAccentSecondary
        averageStrokeRateLabel.textColor = UIColor.dragonAccentSecondary
        substitutionTimingsLabel.textColor = UIColor.dragonAccentSecondary
    }

    private func parseTime(_ timeString: String) -> TimeInterval {
        let components = timeString.components(separatedBy: ":")
        if components.count == 2,
           let minutes = Double(components[0]),
           let seconds = Double(components[1]) {
            return (minutes * 60) + seconds
        }
        return 120.0 // Default 2 minutes
    }

    private func showAlert(title: String, message: String, completion: (() -> Void)? = nil) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion?()
        })
        present(alert, animated: true)
    }
}

// MARK: - PhaseConfigViewDelegate

extension StrokeRateSimulatorViewController: PhaseConfigViewDelegate {
    func phaseConfigView(_ view: PhaseConfigView, didRequestRemoval: Bool) {
        if let index = phaseViews.firstIndex(of: view) {
            phaseViews.remove(at: index)
            phasesStackView.removeArrangedSubview(view)
            view.removeFromSuperview()

            // 重新计算
            if !phaseViews.isEmpty {
                calculatePressed()
            }
        }
    }

    func phaseConfigViewDidChange(_ view: PhaseConfigView) {
        // 阶段配置变化时重新计算
        calculatePressed()
    }
}
