//
//  StrategyTemplate.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import Foundation

/// Factory class for creating predefined strategy templates
class StrategyTemplate {
    
    // MARK: - Template Creation
    
    /// Create an even pace strategy
    /// - Parameters:
    ///   - distance: Race distance in meters
    ///   - targetTime: Target finish time in seconds
    ///   - strokeDistance: Average stroke distance in meters
    /// - Returns: Even pace strategy
    static func createEvenPaceStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> PaceStrategy {
        let strategy = PaceStrategy(
            name: "Even Pace Strategy",
            raceDistance: distance,
            targetTime: targetTime,
            averageStrokeDistance: strokeDistance,
            strategyType: .evenPace,
            isCustom: false
        )
        
        let targetSpeed = distance / targetTime
        let strokeRate = (targetSpeed / strokeDistance) * 60 // SPM
        
        // Single phase covering entire race
        let mainPhase = RhythmPhase(
            name: "Even Pace",
            duration: targetTime,
            strokeRate: strokeRate,
            distance: distance,
            strokeDistance: strokeDistance,
            phaseType: .middle
        )
        
        strategy.addPhase(mainPhase)
        return strategy
    }
    
    /// Create a start burst strategy
    /// - Parameters:
    ///   - distance: Race distance in meters
    ///   - targetTime: Target finish time in seconds
    ///   - strokeDistance: Average stroke distance in meters
    /// - Returns: Start burst strategy
    static func createStartBurstStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> PaceStrategy {
        let strategy = PaceStrategy(
            name: "Start Burst Strategy",
            raceDistance: distance,
            targetTime: targetTime,
            averageStrokeDistance: strokeDistance,
            strategyType: .startBurst,
            isCustom: false
        )
        
        let averageSpeed = distance / targetTime
        
        // Start phase: 15% of time, 20% faster than average
        let startDuration = targetTime * 0.15
        let startSpeed = averageSpeed * 1.2
        let startDistance = startSpeed * startDuration
        let startStrokeRate = (startSpeed / strokeDistance) * 60
        
        let startPhase = RhythmPhase(
            name: "Start Burst",
            duration: startDuration,
            strokeRate: startStrokeRate,
            distance: startDistance,
            strokeDistance: strokeDistance,
            phaseType: .start
        )
        
        // Main phase: remaining distance and time
        let mainDuration = targetTime - startDuration
        let mainDistance = distance - startDistance
        let mainSpeed = mainDistance / mainDuration
        let mainStrokeRate = (mainSpeed / strokeDistance) * 60
        
        let mainPhase = RhythmPhase(
            name: "Settle & Maintain",
            duration: mainDuration,
            strokeRate: mainStrokeRate,
            distance: mainDistance,
            strokeDistance: strokeDistance,
            phaseType: .middle,
            allowsSubstitution: true
        )
        mainPhase.substitutionTiming = 0.3 // 30% into main phase
        
        strategy.addPhase(startPhase)
        strategy.addPhase(mainPhase)
        return strategy
    }
    
    /// Create a finish sprint strategy
    /// - Parameters:
    ///   - distance: Race distance in meters
    ///   - targetTime: Target finish time in seconds
    ///   - strokeDistance: Average stroke distance in meters
    /// - Returns: Finish sprint strategy
    static func createFinishSprintStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> PaceStrategy {
        let strategy = PaceStrategy(
            name: "Finish Sprint Strategy",
            raceDistance: distance,
            targetTime: targetTime,
            averageStrokeDistance: strokeDistance,
            strategyType: .finishSprint,
            isCustom: false
        )
        
        let averageSpeed = distance / targetTime
        
        // Sprint phase: last 20% of time, 25% faster than average
        let sprintDuration = targetTime * 0.2
        let sprintSpeed = averageSpeed * 1.25
        let sprintDistance = sprintSpeed * sprintDuration
        let sprintStrokeRate = (sprintSpeed / strokeDistance) * 60
        
        // Main phase: first 80% of race
        let mainDuration = targetTime - sprintDuration
        let mainDistance = distance - sprintDistance
        let mainSpeed = mainDistance / mainDuration
        let mainStrokeRate = (mainSpeed / strokeDistance) * 60
        
        let mainPhase = RhythmPhase(
            name: "Build Up",
            duration: mainDuration,
            strokeRate: mainStrokeRate,
            distance: mainDistance,
            strokeDistance: strokeDistance,
            phaseType: .middle,
            allowsSubstitution: true
        )
        mainPhase.substitutionTiming = 0.7 // 70% into main phase
        
        let sprintPhase = RhythmPhase(
            name: "Final Sprint",
            duration: sprintDuration,
            strokeRate: sprintStrokeRate,
            distance: sprintDistance,
            strokeDistance: strokeDistance,
            phaseType: .sprint
        )
        
        strategy.addPhase(mainPhase)
        strategy.addPhase(sprintPhase)
        return strategy
    }
    
    /// Create a negative split strategy (start slow, finish fast)
    /// - Parameters:
    ///   - distance: Race distance in meters
    ///   - targetTime: Target finish time in seconds
    ///   - strokeDistance: Average stroke distance in meters
    /// - Returns: Negative split strategy
    static func createNegativeSplitStrategy(distance: Double, targetTime: TimeInterval, strokeDistance: Double = 2.5) -> PaceStrategy {
        let strategy = PaceStrategy(
            name: "Negative Split Strategy",
            raceDistance: distance,
            targetTime: targetTime,
            averageStrokeDistance: strokeDistance,
            strategyType: .negative,
            isCustom: false
        )
        
        let averageSpeed = distance / targetTime
        let halfDistance = distance / 2
        let halfTime = targetTime / 2
        
        // First half: 10% slower than average
        let firstHalfSpeed = averageSpeed * 0.9
        let firstHalfDuration = halfDistance / firstHalfSpeed
        let firstHalfStrokeRate = (firstHalfSpeed / strokeDistance) * 60
        
        // Second half: faster to compensate
        let secondHalfDuration = targetTime - firstHalfDuration
        let secondHalfSpeed = halfDistance / secondHalfDuration
        let secondHalfStrokeRate = (secondHalfSpeed / strokeDistance) * 60
        
        let firstHalf = RhythmPhase(
            name: "Conservative Start",
            duration: firstHalfDuration,
            strokeRate: firstHalfStrokeRate,
            distance: halfDistance,
            strokeDistance: strokeDistance,
            phaseType: .start,
            allowsSubstitution: true
        )
        firstHalf.substitutionTiming = 0.8
        
        let secondHalf = RhythmPhase(
            name: "Strong Finish",
            duration: secondHalfDuration,
            strokeRate: secondHalfStrokeRate,
            distance: halfDistance,
            strokeDistance: strokeDistance,
            phaseType: .sprint
        )
        
        strategy.addPhase(firstHalf)
        strategy.addPhase(secondHalf)
        return strategy
    }
    
    /// Create a three-phase strategy with custom phase distribution
    /// - Parameters:
    ///   - distance: Race distance in meters
    ///   - targetTime: Target finish time in seconds
    ///   - strokeDistance: Average stroke distance in meters
    ///   - startPercentage: Percentage of race for start phase (0.0-1.0)
    ///   - sprintPercentage: Percentage of race for sprint phase (0.0-1.0)
    /// - Returns: Three-phase strategy
    static func createThreePhaseStrategy(distance: Double, 
                                       targetTime: TimeInterval, 
                                       strokeDistance: Double = 2.5,
                                       startPercentage: Double = 0.2,
                                       sprintPercentage: Double = 0.2) -> PaceStrategy {
        let strategy = PaceStrategy(
            name: "Three Phase Strategy",
            raceDistance: distance,
            targetTime: targetTime,
            averageStrokeDistance: strokeDistance,
            strategyType: .custom,
            isCustom: false
        )
        
        let averageSpeed = distance / targetTime
        let middlePercentage = 1.0 - startPercentage - sprintPercentage
        
        // Start phase: 15% faster than average
        let startDuration = targetTime * startPercentage
        let startSpeed = averageSpeed * 1.15
        let startDistance = startSpeed * startDuration
        let startStrokeRate = (startSpeed / strokeDistance) * 60
        
        // Sprint phase: 20% faster than average
        let sprintDuration = targetTime * sprintPercentage
        let sprintSpeed = averageSpeed * 1.2
        let sprintDistance = sprintSpeed * sprintDuration
        let sprintStrokeRate = (sprintSpeed / strokeDistance) * 60
        
        // Middle phase: remaining distance and time
        let middleDuration = targetTime * middlePercentage
        let middleDistance = distance - startDistance - sprintDistance
        let middleSpeed = middleDistance / middleDuration
        let middleStrokeRate = (middleSpeed / strokeDistance) * 60
        
        let startPhase = RhythmPhase(
            name: "Start",
            duration: startDuration,
            strokeRate: startStrokeRate,
            distance: startDistance,
            strokeDistance: strokeDistance,
            phaseType: .start
        )
        
        let middlePhase = RhythmPhase(
            name: "Middle",
            duration: middleDuration,
            strokeRate: middleStrokeRate,
            distance: middleDistance,
            strokeDistance: strokeDistance,
            phaseType: .middle,
            allowsSubstitution: true
        )
        middlePhase.substitutionTiming = 0.5
        
        let sprintPhase = RhythmPhase(
            name: "Sprint",
            duration: sprintDuration,
            strokeRate: sprintStrokeRate,
            distance: sprintDistance,
            strokeDistance: strokeDistance,
            phaseType: .sprint
        )
        
        strategy.addPhase(startPhase)
        strategy.addPhase(middlePhase)
        strategy.addPhase(sprintPhase)
        return strategy
    }
    
    /// Get all available template types
    /// - Returns: Array of strategy types that have templates
    static func getAvailableTemplates() -> [StrategyType] {
        return [.evenPace, .startBurst, .finishSprint, .negative]
    }
    
    /// Create strategy from template type
    /// - Parameters:
    ///   - type: Strategy type
    ///   - distance: Race distance
    ///   - targetTime: Target time
    ///   - strokeDistance: Stroke distance
    /// - Returns: Strategy based on template
    static func createStrategy(type: StrategyType, 
                             distance: Double, 
                             targetTime: TimeInterval, 
                             strokeDistance: Double = 2.5) -> PaceStrategy {
        switch type {
        case .evenPace:
            return createEvenPaceStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .startBurst:
            return createStartBurstStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .finishSprint:
            return createFinishSprintStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .negative:
            return createNegativeSplitStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        case .positive:
            return createThreePhaseStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance, startPercentage: 0.3, sprintPercentage: 0.1)
        case .custom:
            return createEvenPaceStrategy(distance: distance, targetTime: targetTime, strokeDistance: strokeDistance)
        }
    }
}
