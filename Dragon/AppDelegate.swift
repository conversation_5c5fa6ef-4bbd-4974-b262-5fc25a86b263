//
//  AppDelegate.swift
//  Dragon
//
//  Created by jj on 2025/6/1.
//

import UIKit
import IQKeyboardManagerSwift

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.

        // Setup IQKeyboardManager
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.enableAutoToolbar = true

        // Setup window and root view controller
        window = UIWindow(frame: UIScreen.main.bounds)
        let tabBarController = MainTabBarController()

        // Configure navigation bar appearance for dark theme
        setupNavigationBarAppearance()

        window?.rootViewController = tabBarController
        window?.makeKeyAndVisible()

        return true
    }

    private func setupNavigationBarAppearance() {
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.dragonDarkBackground
        appearance.titleTextAttributes = [.foregroundColor: UIColor.dragonTextPrimary]
        appearance.largeTitleTextAttributes = [.foregroundColor: UIColor.dragonTextPrimary]

        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().tintColor = UIColor.dragonAccent

        // Ensure back button is visible
        UINavigationBar.appearance().backIndicatorImage = UIImage(systemName: "chevron.left")
        UINavigationBar.appearance().backIndicatorTransitionMaskImage = UIImage(systemName: "chevron.left")
    }
}

