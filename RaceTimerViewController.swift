// MARK: - TeamTimerViewDelegate

extension RaceTimerViewController: TeamTimerViewDelegate {
    func teamTimerView(_ teamTimerView: TeamTimerView, didFinishTeam team: Team) {
        print("TeamTimerViewDelegate called for team: \(team.name)")
        
        // 确保在主线程上调用 UI 相关操作
        DispatchQueue.main.async {
            // 视觉反馈，表明按钮已被点击
            if let index = self.teamTimerViews.firstIndex(where: { $0.team.id == team.id }) {
                self.teamTimerViews[index].animateFinish()
            }
            
            let success = self.raceManager.finishTeam(team)
            print("RaceManager.finishTeam returned: \(success)")
            
            // 如果成功完成，更新 UI
            if success {
                self.updateUI()
            }
        }
    }
} 