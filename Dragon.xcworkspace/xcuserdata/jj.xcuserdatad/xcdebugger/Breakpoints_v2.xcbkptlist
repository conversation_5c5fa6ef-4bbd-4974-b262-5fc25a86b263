<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "80586352-C372-4D1A-92A8-8F3BF1F8CCB9"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "914853ED-799D-4430-821F-F917E47CA4AC"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "Dragon/RaceTimerViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "299"
            endingLineNumber = "299"
            landmarkName = "teamTimerView(_:didFinishTeam:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "914853ED-799D-4430-821F-F917E47CA4AC - 78831013d3ccf47b"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "Dragon.RaceTimerViewController.teamTimerView(_: Dragon.TeamTimerView, didFinishTeam: Dragon.Team) -&gt; ()"
                  moduleName = "Dragon.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/2025-01/Dragon/Dragon/RaceTimerViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "300"
                  endingLineNumber = "300">
               </Location>
               <Location
                  uuid = "914853ED-799D-4430-821F-F917E47CA4AC - 6c8364130b4e0e84"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 @Swift.MainActor () -&gt; () in Dragon.RaceTimerViewController.teamTimerView(_: Dragon.TeamTimerView, didFinishTeam: Dragon.Team) -&gt; ()"
                  moduleName = "Dragon.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Desktop/2025-01/Dragon/Dragon/RaceTimerViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "302"
                  endingLineNumber = "302">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
