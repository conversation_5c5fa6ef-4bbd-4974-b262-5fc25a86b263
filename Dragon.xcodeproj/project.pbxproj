// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2804BC512DEC6C54004238E2 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2804BC482DEC6C54004238E2 /* Assets.xcassets */; };
		2804BC532DEC6C54004238E2 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2804BC4B2DEC6C54004238E2 /* LaunchScreen.storyboard */; };
		2804BC552DEC6C54004238E2 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC472DEC6C54004238E2 /* AppDelegate.swift */; };
		2804BC5A2DEC6DAE004238E2 /* Team.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC582DEC6DAE004238E2 /* Team.swift */; };
		2804BC5B2DEC6DAE004238E2 /* UIColor+Theme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC592DEC6DAE004238E2 /* UIColor+Theme.swift */; };
		2804BC5D2DEC6DB3004238E2 /* TimeFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC5C2DEC6DB3004238E2 /* TimeFormatter.swift */; };
		2804BC5F2DEC6DDF004238E2 /* RaceRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC5E2DEC6DDF004238E2 /* RaceRecord.swift */; };
		2804BC612DEC6DFE004238E2 /* RaceManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC602DEC6DFE004238E2 /* RaceManager.swift */; };
		2804BC632DEC6E1C004238E2 /* MainViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC622DEC6E1C004238E2 /* MainViewController.swift */; };
		2804BC652DEC6E61004238E2 /* CreateRaceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC642DEC6E61004238E2 /* CreateRaceViewController.swift */; };
		2804BC672DEC6E81004238E2 /* TeamInputView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC662DEC6E81004238E2 /* TeamInputView.swift */; };
		2804BC692DEC6EA3004238E2 /* RaceTimerViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC682DEC6EA3004238E2 /* RaceTimerViewController.swift */; };
		2804BC6B2DEC6EC7004238E2 /* TimerDisplayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC6A2DEC6EC7004238E2 /* TimerDisplayView.swift */; };
		2804BC6D2DEC6F11004238E2 /* RaceResultsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC6C2DEC6F11004238E2 /* RaceResultsViewController.swift */; };
		2804BC6F2DEC6F1A004238E2 /* TeamTimerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC6E2DEC6F1A004238E2 /* TeamTimerView.swift */; };
		2804BC8B2DEC8041004238E2 /* StrokeRateSimulatorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC8A2DEC8041004238E2 /* StrokeRateSimulatorViewController.swift */; };
		2804BC8C2DEC8041004238E2 /* RacePhase.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC862DEC8041004238E2 /* RacePhase.swift */; };
		2804BC8D2DEC8041004238E2 /* StrategySimulatorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC882DEC8041004238E2 /* StrategySimulatorViewController.swift */; };
		2804BC8E2DEC8041004238E2 /* StrategyManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC872DEC8041004238E2 /* StrategyManager.swift */; };
		2804BC8F2DEC8041004238E2 /* StrategyTemplateGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC892DEC8041004238E2 /* StrategyTemplateGenerator.swift */; };
		2804BC912DEC8075004238E2 /* PhaseConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC902DEC8075004238E2 /* PhaseConfigView.swift */; };
		2804BC932DEC80D1004238E2 /* StrategyTemplatesViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC922DEC80D1004238E2 /* StrategyTemplatesViewController.swift */; };
		2804BC952DEC814B004238E2 /* StrokeCalculatorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC942DEC814B004238E2 /* StrokeCalculatorViewController.swift */; };
		2804BC972DEC815C004238E2 /* PerformanceSimulatorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC962DEC815C004238E2 /* PerformanceSimulatorViewController.swift */; };
		2804BC992DEC81BB004238E2 /* StrategyEditorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2804BC982DEC81BB004238E2 /* StrategyEditorViewController.swift */; };
		92A6931126872FF1F5D541ED /* Pods_Dragon.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D588E2B25B78E8EC39035266 /* Pods_Dragon.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2804BC2F2DEC6C50004238E2 /* Dragon.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Dragon.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2804BC472DEC6C54004238E2 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		2804BC482DEC6C54004238E2 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2804BC492DEC6C54004238E2 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		2804BC4A2DEC6C54004238E2 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		2804BC582DEC6DAE004238E2 /* Team.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Team.swift; sourceTree = "<group>"; };
		2804BC592DEC6DAE004238E2 /* UIColor+Theme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Theme.swift"; sourceTree = "<group>"; };
		2804BC5C2DEC6DB3004238E2 /* TimeFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeFormatter.swift; sourceTree = "<group>"; };
		2804BC5E2DEC6DDF004238E2 /* RaceRecord.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RaceRecord.swift; sourceTree = "<group>"; };
		2804BC602DEC6DFE004238E2 /* RaceManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RaceManager.swift; sourceTree = "<group>"; };
		2804BC622DEC6E1C004238E2 /* MainViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainViewController.swift; sourceTree = "<group>"; };
		2804BC642DEC6E61004238E2 /* CreateRaceViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateRaceViewController.swift; sourceTree = "<group>"; };
		2804BC662DEC6E81004238E2 /* TeamInputView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TeamInputView.swift; sourceTree = "<group>"; };
		2804BC682DEC6EA3004238E2 /* RaceTimerViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RaceTimerViewController.swift; sourceTree = "<group>"; };
		2804BC6A2DEC6EC7004238E2 /* TimerDisplayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimerDisplayView.swift; sourceTree = "<group>"; };
		2804BC6C2DEC6F11004238E2 /* RaceResultsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RaceResultsViewController.swift; sourceTree = "<group>"; };
		2804BC6E2DEC6F1A004238E2 /* TeamTimerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TeamTimerView.swift; sourceTree = "<group>"; };
		2804BC862DEC8041004238E2 /* RacePhase.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RacePhase.swift; sourceTree = "<group>"; };
		2804BC872DEC8041004238E2 /* StrategyManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategyManager.swift; sourceTree = "<group>"; };
		2804BC882DEC8041004238E2 /* StrategySimulatorViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategySimulatorViewController.swift; sourceTree = "<group>"; };
		2804BC892DEC8041004238E2 /* StrategyTemplateGenerator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategyTemplateGenerator.swift; sourceTree = "<group>"; };
		2804BC8A2DEC8041004238E2 /* StrokeRateSimulatorViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrokeRateSimulatorViewController.swift; sourceTree = "<group>"; };
		2804BC902DEC8075004238E2 /* PhaseConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PhaseConfigView.swift; sourceTree = "<group>"; };
		2804BC922DEC80D1004238E2 /* StrategyTemplatesViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategyTemplatesViewController.swift; sourceTree = "<group>"; };
		2804BC942DEC814B004238E2 /* StrokeCalculatorViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrokeCalculatorViewController.swift; sourceTree = "<group>"; };
		2804BC962DEC815C004238E2 /* PerformanceSimulatorViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PerformanceSimulatorViewController.swift; sourceTree = "<group>"; };
		2804BC982DEC81BB004238E2 /* StrategyEditorViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StrategyEditorViewController.swift; sourceTree = "<group>"; };
		734C35F61170F35774E7FBB7 /* Pods-Dragon.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dragon.debug.xcconfig"; path = "Target Support Files/Pods-Dragon/Pods-Dragon.debug.xcconfig"; sourceTree = "<group>"; };
		A705942A3265E02B576BCFD2 /* Pods-Dragon.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dragon.release.xcconfig"; path = "Target Support Files/Pods-Dragon/Pods-Dragon.release.xcconfig"; sourceTree = "<group>"; };
		D588E2B25B78E8EC39035266 /* Pods_Dragon.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Dragon.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2804BC2C2DEC6C50004238E2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				92A6931126872FF1F5D541ED /* Pods_Dragon.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2804BC262DEC6C50004238E2 = {
			isa = PBXGroup;
			children = (
				2804BC502DEC6C54004238E2 /* Dragon */,
				2804BC302DEC6C50004238E2 /* Products */,
				439F5C459EFBB6D8E98EEC00 /* Pods */,
				617F81951D60D4FF32C9B2D6 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2804BC302DEC6C50004238E2 /* Products */ = {
			isa = PBXGroup;
			children = (
				2804BC2F2DEC6C50004238E2 /* Dragon.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2804BC502DEC6C54004238E2 /* Dragon */ = {
			isa = PBXGroup;
			children = (
				2804BC982DEC81BB004238E2 /* StrategyEditorViewController.swift */,
				2804BC962DEC815C004238E2 /* PerformanceSimulatorViewController.swift */,
				2804BC942DEC814B004238E2 /* StrokeCalculatorViewController.swift */,
				2804BC922DEC80D1004238E2 /* StrategyTemplatesViewController.swift */,
				2804BC902DEC8075004238E2 /* PhaseConfigView.swift */,
				2804BC862DEC8041004238E2 /* RacePhase.swift */,
				2804BC872DEC8041004238E2 /* StrategyManager.swift */,
				2804BC882DEC8041004238E2 /* StrategySimulatorViewController.swift */,
				2804BC892DEC8041004238E2 /* StrategyTemplateGenerator.swift */,
				2804BC8A2DEC8041004238E2 /* StrokeRateSimulatorViewController.swift */,
				2804BC6E2DEC6F1A004238E2 /* TeamTimerView.swift */,
				2804BC6C2DEC6F11004238E2 /* RaceResultsViewController.swift */,
				2804BC6A2DEC6EC7004238E2 /* TimerDisplayView.swift */,
				2804BC682DEC6EA3004238E2 /* RaceTimerViewController.swift */,
				2804BC662DEC6E81004238E2 /* TeamInputView.swift */,
				2804BC642DEC6E61004238E2 /* CreateRaceViewController.swift */,
				2804BC622DEC6E1C004238E2 /* MainViewController.swift */,
				2804BC602DEC6DFE004238E2 /* RaceManager.swift */,
				2804BC5E2DEC6DDF004238E2 /* RaceRecord.swift */,
				2804BC5C2DEC6DB3004238E2 /* TimeFormatter.swift */,
				2804BC582DEC6DAE004238E2 /* Team.swift */,
				2804BC592DEC6DAE004238E2 /* UIColor+Theme.swift */,
				2804BC472DEC6C54004238E2 /* AppDelegate.swift */,
				2804BC482DEC6C54004238E2 /* Assets.xcassets */,
				2804BC492DEC6C54004238E2 /* Info.plist */,
				2804BC4B2DEC6C54004238E2 /* LaunchScreen.storyboard */,
			);
			path = Dragon;
			sourceTree = "<group>";
		};
		439F5C459EFBB6D8E98EEC00 /* Pods */ = {
			isa = PBXGroup;
			children = (
				734C35F61170F35774E7FBB7 /* Pods-Dragon.debug.xcconfig */,
				A705942A3265E02B576BCFD2 /* Pods-Dragon.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		617F81951D60D4FF32C9B2D6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				D588E2B25B78E8EC39035266 /* Pods_Dragon.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2804BC2E2DEC6C50004238E2 /* Dragon */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2804BC422DEC6C51004238E2 /* Build configuration list for PBXNativeTarget "Dragon" */;
			buildPhases = (
				A3CF71D40061F4B7162F0500 /* [CP] Check Pods Manifest.lock */,
				2804BC2B2DEC6C50004238E2 /* Sources */,
				2804BC2C2DEC6C50004238E2 /* Frameworks */,
				2804BC2D2DEC6C50004238E2 /* Resources */,
				B9CC375F60218CFB3F0E90FC /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Dragon;
			productName = Dragon;
			productReference = 2804BC2F2DEC6C50004238E2 /* Dragon.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2804BC272DEC6C50004238E2 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					2804BC2E2DEC6C50004238E2 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 2804BC2A2DEC6C50004238E2 /* Build configuration list for PBXProject "Dragon" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2804BC262DEC6C50004238E2;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2804BC302DEC6C50004238E2 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2804BC2E2DEC6C50004238E2 /* Dragon */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2804BC2D2DEC6C50004238E2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2804BC512DEC6C54004238E2 /* Assets.xcassets in Resources */,
				2804BC532DEC6C54004238E2 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		A3CF71D40061F4B7162F0500 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Dragon-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B9CC375F60218CFB3F0E90FC /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dragon/Pods-Dragon-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dragon/Pods-Dragon-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Dragon/Pods-Dragon-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2804BC2B2DEC6C50004238E2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2804BC632DEC6E1C004238E2 /* MainViewController.swift in Sources */,
				2804BC932DEC80D1004238E2 /* StrategyTemplatesViewController.swift in Sources */,
				2804BC552DEC6C54004238E2 /* AppDelegate.swift in Sources */,
				2804BC952DEC814B004238E2 /* StrokeCalculatorViewController.swift in Sources */,
				2804BC992DEC81BB004238E2 /* StrategyEditorViewController.swift in Sources */,
				2804BC672DEC6E81004238E2 /* TeamInputView.swift in Sources */,
				2804BC5D2DEC6DB3004238E2 /* TimeFormatter.swift in Sources */,
				2804BC5A2DEC6DAE004238E2 /* Team.swift in Sources */,
				2804BC692DEC6EA3004238E2 /* RaceTimerViewController.swift in Sources */,
				2804BC6D2DEC6F11004238E2 /* RaceResultsViewController.swift in Sources */,
				2804BC8B2DEC8041004238E2 /* StrokeRateSimulatorViewController.swift in Sources */,
				2804BC912DEC8075004238E2 /* PhaseConfigView.swift in Sources */,
				2804BC8C2DEC8041004238E2 /* RacePhase.swift in Sources */,
				2804BC8D2DEC8041004238E2 /* StrategySimulatorViewController.swift in Sources */,
				2804BC8E2DEC8041004238E2 /* StrategyManager.swift in Sources */,
				2804BC8F2DEC8041004238E2 /* StrategyTemplateGenerator.swift in Sources */,
				2804BC5B2DEC6DAE004238E2 /* UIColor+Theme.swift in Sources */,
				2804BC612DEC6DFE004238E2 /* RaceManager.swift in Sources */,
				2804BC6B2DEC6EC7004238E2 /* TimerDisplayView.swift in Sources */,
				2804BC6F2DEC6F1A004238E2 /* TeamTimerView.swift in Sources */,
				2804BC5F2DEC6DDF004238E2 /* RaceRecord.swift in Sources */,
				2804BC972DEC815C004238E2 /* PerformanceSimulatorViewController.swift in Sources */,
				2804BC652DEC6E61004238E2 /* CreateRaceViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		2804BC4B2DEC6C54004238E2 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				2804BC4A2DEC6C54004238E2 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		2804BC432DEC6C51004238E2 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 734C35F61170F35774E7FBB7 /* Pods-Dragon.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Dragon/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.Dragon;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		2804BC442DEC6C51004238E2 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A705942A3265E02B576BCFD2 /* Pods-Dragon.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Dragon/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.Dragon;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		2804BC452DEC6C51004238E2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2804BC462DEC6C51004238E2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2804BC2A2DEC6C50004238E2 /* Build configuration list for PBXProject "Dragon" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2804BC452DEC6C51004238E2 /* Debug */,
				2804BC462DEC6C51004238E2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2804BC422DEC6C51004238E2 /* Build configuration list for PBXNativeTarget "Dragon" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2804BC432DEC6C51004238E2 /* Debug */,
				2804BC442DEC6C51004238E2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2804BC272DEC6C50004238E2 /* Project object */;
}
